import {AppointmentSchedule, SpecialSchedule} from "@/types/professional-schedules";
import {getDateKey} from "@/utils/dateUtils";


export const createIsDayAvailableChecker = (
    datesWithVacationSchedules: Set<Date>,
    specialSchedulesByDate: Record<string, SpecialSchedule[]>,
    appointmentSchedulesByDayOfWeek: Record<string, AppointmentSchedule[]>
) => {

    return (day: Date): boolean => {
        if (datesWithVacationSchedules.has(day)) {
            return false;
        }
        const specialSchedulesForDay: SpecialSchedule[] | undefined = specialSchedulesByDate[getDateKey(day)];
        const dayOfWeek: string = day.toLocaleDateString('en-US', {weekday: 'long'}).toUpperCase();
        const appointmentSchedulesForDay: AppointmentSchedule[] | undefined = appointmentSchedulesByDayOfWeek[dayOfWeek];
        return !!(specialSchedulesForDay || appointmentSchedulesForDay);

    };
};