import { NextResponse } from 'next/server'

export async function GET() {
    try {
        const config = {
            backendUrl: process.env.BACKEND_URL || ""
        }
        
        return NextResponse.json(config)
    } catch (error) {
        console.error('Error getting config:', error)
        return NextResponse.json(
            { error: 'Failed to get configuration' },
            { status: 500 }
        )
    }
}
