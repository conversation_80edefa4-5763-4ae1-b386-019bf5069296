"use client"

import axios from "axios";
import {ProfessionalSchedulesResponse} from "@/types/professional-schedules";


export function getMonthYearFormatForAgendasFromDate(date: Date): string {
    return `${date.getFullYear()}-${getMonthNameFromDate(date)}`
}

function getMonthNameFromDate(date: Date): string {
    const months = [
        'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE',
        'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'
    ];
    return months[date.getMonth()];
}

export const getSchedules = async (medicalCenterId: number, doctorId: number, employeeUserId: number, date: Date): Promise<ProfessionalSchedulesResponse> => {
    try {
        const month = getMonthNameFromDate(date);
        const year = date.getFullYear();
        const response = await axios.get(
            `/api/professional-schedules?medicalCenterId=${medicalCenterId}&professionalId=${doctorId}&month=${month}&year=${year}&employeeUserId=${employeeUserId}`
        );
        if (response.status === 200) {
            return ProfessionalSchedulesResponse.fromJSON(response.data);

        }
        return new ProfessionalSchedulesResponse(year.toString(), month, [], [], [], [], [])
    } catch (error) {
        console.error('Error fetching professional schedules:', error);
        return new ProfessionalSchedulesResponse(date.getFullYear().toString(), getMonthNameFromDate(date), [], [], [], [], [])
    }
}
