import {NextRequest, NextResponse} from 'next/server';
import {AppointmentCreationRequestFromMedicalCenter} from '@/app/api/requestBodies/AppointmentCreationRequestFromMedicalCenter';

// Valid origin values
const VALID_ORIGINS = ['TURNERA', 'PHONE', 'IN_PERSON'] as const;
type ValidOrigin = typeof VALID_ORIGINS[number];

export async function POST(
    request: NextRequest,
    { params }: { params: { origin: string } }
) {
    try {
        const { origin } = params;

        // Validate origin parameter
        if (!VALID_ORIGINS.includes(origin as ValidOrigin)) {
            return NextResponse.json(
                {
                    error: 'Invalid origin parameter',
                    details: `Origin must be one of: ${VALID_ORIGINS.join(', ')}`
                },
                { status: 400 }
            );
        }

        const body: AppointmentCreationRequestFromMedicalCenter = await request.json();

        // Validate required fields
        const requiredFields: (keyof AppointmentCreationRequestFromMedicalCenter)[] = [
            'medicalCenterId',
            'professionalId',
            'patientId',
            'consultationTypeIds',
            'employeeUserId',
            'date',
            'startTime'
        ];

        for (const field of requiredFields) {
            if (body[field] === undefined || body[field] === null) {
                return NextResponse.json(
                    { error: `Missing required field: ${field}` },
                    { status: 400 }
                );
            }
        }

        // Validate date format (dd-MM-yyyy)
        const dateRegex = /^\d{2}-\d{2}-\d{4}$/;
        if (!dateRegex.test(body.date)) {
            return NextResponse.json(
                { error: 'Invalid date format. Expected dd-MM-yyyy' },
                { status: 400 }
            );
        }

        // Validate time format (HH:mm:ss)
        const timeRegex = /^\d{2}:\d{2}:\d{2}$/;
        if (!timeRegex.test(body.startTime)) {
            return NextResponse.json(
                { error: 'Invalid time format. Expected HH:mm:ss' },
                { status: 400 }
            );
        }

        // Validate consultationTypeIds is not empty
        if (!Array.isArray(body.consultationTypeIds) || body.consultationTypeIds.length === 0) {
            return NextResponse.json(
                { error: 'consultationTypeIds must be a non-empty array' },
                { status: 400 }
            );
        }

        // Make request to external API
        const response = await fetch(
            `${process.env.BACKEND_URL}/appointment/from-medical-center/${origin}`,
            {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            console.error('External API error:', response.status, errorText);

            return NextResponse.json(
                {
                    error: 'Failed to create appointment',
                    details: errorText,
                    status: response.status
                },
                { status: response.status }
            );
        }

        const result = await response.json();
        return NextResponse.json(result, { status: 201 });

    } catch (error) {
        console.error('Error creating appointment:', error);

        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}
