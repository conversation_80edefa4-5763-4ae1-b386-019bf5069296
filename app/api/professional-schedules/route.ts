import {NextRequest, NextResponse} from 'next/server';

// Helper function to get current month in uppercase (fallback)
function getCurrentMonthUppercase(): string {
    const months = [
        'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE',
        'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'
    ];
    const currentMonth = new Date().getMonth();
    return months[currentMonth];
}

// Helper function to validate and format month parameter
function validateAndFormatMonth(monthParam: string | null): string {
    if (!monthParam) {
        return getCurrentMonthUppercase(); // Fallback to current month
    }

    const validMonths = [
        'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE',
        'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'
    ];

    const upperMonth = monthParam.toUpperCase();

    if (validMonths.includes(upperMonth)) {
        return upperMonth;
    }

    // If invalid month provided, fallback to current month
    return getCurrentMonthUppercase();
}

export async function GET(request: NextRequest) {
    try {
        // Extract query parameters
        const {searchParams} = new URL(request.url);
        const medicalCenterId = searchParams.get('medicalCenterId');
        const professionalId = searchParams.get('professionalId');
        const monthParam = searchParams.get('month');
        const yearParam = searchParams.get('year');
        const employeeUserId = searchParams.get('employeeUserId');

        // Validate required parameters
        if (!medicalCenterId || !professionalId) {
            return NextResponse.json(
                {error: 'Missing required parameters: medicalCenterId and professionalId are required'},
                {status: 400}
            );
        }

        // Validate and format month parameter
        const month = validateAndFormatMonth(monthParam);

        // Build the API URL
        const apiUrl = `${process.env.BACKEND_URL}/medical-center/${medicalCenterId}/professional/${professionalId}/schedule?employeeUserId=${employeeUserId}&month=${month}&year=${yearParam}`;

        console.log('Fetching professional schedules from:', apiUrl);

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        return NextResponse.json(data, {
            status: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            },
        });
    } catch (error) {
        console.error('Error fetching professional schedules:', error);

        return NextResponse.json(
            {error: 'Failed to fetch professional schedules'},
            {
                status: 500,
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                },
            }
        );
    }
}

export async function OPTIONS(request: NextRequest) {
    return new NextResponse(null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
    });
}
