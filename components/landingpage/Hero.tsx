"use client"

import { useState, useEffect } from "react"
import { getLocationNamesFromMedicalCenters } from "@/data/locations"

export default function Hero() {
  // Get neighborhoods from medical centers
  const [neighborhoods, setNeighborhoods] = useState<string[]>([])
  const [shuffledNeighborhoods, setShuffledNeighborhoods] = useState<number[]>([])
  const [currentPosition, setCurrentPosition] = useState(0)
  const [currentNeighborhood, setCurrentNeighborhood] = useState(0)
  const [nextNeighborhood, setNextNeighborhood] = useState(1)
  const [isAnimating, setIsAnimating] = useState(false)

  // Fisher-Yates shuffle algorithm to create a random order
  const shuffleArray = (length: number): number[] => {
    const array = Array.from({ length }, (_, i) => i);
    for (let i = length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  };

  // Initialize neighborhoods from medical centers
  useEffect(() => {
    // This needs to run only on the client side
    if (typeof window === 'undefined') return;

    try {
      // Get neighborhoods from medical centers
      const locationNames = getLocationNamesFromMedicalCenters();

      // If no locations found or only one location, use CABA locations as fallback
      if (!locationNames || locationNames.length <= 1) {
        // Get 9 popular CABA neighborhoods
        const popularCABANeighborhoods = [
          "Palermo", "Belgrano", "Recoleta", "Caballito",
          "Almagro", "Villa Crespo", "Núñez", "San Telmo", "Colegiales"
        ];
        setNeighborhoods(popularCABANeighborhoods);
      } else {
        setNeighborhoods(locationNames);
      }
    } catch (error) {
      console.error('Error loading neighborhoods:', error);
      // Fallback to popular CABA neighborhoods
      const popularCABANeighborhoods = [
        "Palermo", "Belgrano", "Recoleta", "Caballito",
        "Almagro", "Villa Crespo", "Núñez", "San Telmo", "Colegiales"
      ];
      setNeighborhoods(popularCABANeighborhoods);
    }
  }, []);

  // Initialize with shuffled neighborhoods once we have the data
  useEffect(() => {
    // This needs to run only on the client side
    if (typeof window === 'undefined') return;

    if (neighborhoods.length === 0) return;

    try {
      // Create a shuffled array of indices
      const shuffled = shuffleArray(neighborhoods.length);
      setShuffledNeighborhoods(shuffled);

      // Set initial position
      setCurrentPosition(0);

      // Set initial and next neighborhoods from the shuffled array
      setCurrentNeighborhood(shuffled[0]);
      setNextNeighborhood(shuffled[1 % shuffled.length]);
    } catch (error) {
      console.error('Error initializing neighborhoods:', error);
      // Use default indices if there's an error
      setCurrentNeighborhood(0);
      setNextNeighborhood(0);
    }
  }, [neighborhoods]);

  useEffect(() => {
    // This needs to run only on the client side
    if (typeof window === 'undefined') return;

    if (neighborhoods.length === 0 || shuffledNeighborhoods.length === 0) return;

    try {
      const interval = setInterval(() => {
        setIsAnimating(true)
        setTimeout(() => {
          // Move to the next position in the shuffled array
          const nextPosition = (currentPosition + 1) % shuffledNeighborhoods.length;
          setCurrentPosition(nextPosition);

          // Update current neighborhood to the previous next neighborhood
          setCurrentNeighborhood(nextNeighborhood);

          // Set next neighborhood to the next position in the shuffled array
          const followingPosition = (nextPosition + 1) % shuffledNeighborhoods.length;
          setNextNeighborhood(shuffledNeighborhoods[followingPosition]);

          setIsAnimating(false)
        }, 500)
      }, 2500)

      return () => clearInterval(interval)
    } catch (error) {
      console.error('Error in animation interval:', error);
      // No need to do anything here, just prevent the app from crashing
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPosition, nextNeighborhood, shuffledNeighborhoods])

  return (
    <section className="pt-8 md:pt-20 bg-blue-50 text-black">
      <div className="max-w-7xl mx-auto px-7">
        <div className="text-center">
          <div className="inline-flex items-center gap-2 rounded-full bg-white/70 ring-1 ring-blue-200 px-3 py-1 text-sm text-blue-700 shadow-sm backdrop-blur">
            <span className="inline-block h-2 w-2 rounded-full bg-blue-500" /> Reservá en minutos • Sin llamadas
          </div>

          {/* Mobile version: 3 lines, centered */}
          <h1 className="mt-3 text-[2.5rem] font-semibold font-recoleta text-center leading-[1.1] tracking-tight md:hidden">
            <span className="block">Reservá tu próximo</span>
            <span className="block">turno médico en</span>
            <span className="block">
              {(() => {
                const currentLen = neighborhoods[currentNeighborhood]?.length || 0
                const widthCh = Math.min(Math.max(currentLen, 10), 18)
                return (
                  <span
                    className="neighborhood-carousel align-baseline inline-block text-center mx-auto"
                    style={{ width: `${widthCh}ch` }}
                  >
                    <span className={isAnimating ? 'exiting' : 'current'}>
                      <span className="text-blue-600">{neighborhoods[currentNeighborhood]}</span>
                    </span>
                    <span className={isAnimating ? 'current' : 'entering'}>
                      <span className="text-blue-600">{neighborhoods[nextNeighborhood]}</span>
                    </span>
                  </span>
                )
              })()}
            </span>
          </h1>

          {/* Desktop version: original layout */}
          <h1 className="mt-3 text-4xl md:text-6xl font-semibold font-recoleta text-center leading-tight hidden md:block">
            <span className="block">Reservá tu próximo turno</span>
            <span className="block md:ml-[4ch]">
              médico en{' '}
              {(() => {
                const currentLen = neighborhoods[currentNeighborhood]?.length || 0
                const widthCh = Math.min(Math.max(currentLen, 10), 18)
                return (
                  <span
                    className="neighborhood-carousel align-baseline inline-block text-left"
                    style={{ width: `${widthCh}ch` }}
                  >
                    <span className={isAnimating ? 'exiting' : 'current'}>
                      <span className="text-blue-600">{neighborhoods[currentNeighborhood]}</span>
                    </span>
                    <span className={isAnimating ? 'current' : 'entering'}>
                      <span className="text-blue-600">{neighborhoods[nextNeighborhood]}</span>
                    </span>
                  </span>
                )
              })()}
            </span>
          </h1>
        </div>
      </div>
    </section>
  )
}

function CheckIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      className="h-4 w-4 text-blue-600"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
    </svg>
  )
}

/**
function Stat({ title, value }: { title: string; value: string }) {
  return (
    <div className="rounded-2xl bg-white/70 ring-1 ring-blue-100 px-5 py-4 backdrop-blur text-gray-900">
      <div className="text-2xl md:text-3xl font-bold text-blue-700">{value}</div>
      <div className="text-xs md:text-sm text-gray-600 mt-1">{title}</div>
    </div>
  )
}
*/