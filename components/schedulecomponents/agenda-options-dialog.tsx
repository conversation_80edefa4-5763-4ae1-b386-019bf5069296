"use client"

import {use<PERSON>ffect, use<PERSON>em<PERSON>, useState} from "react"
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle} from "@/components/ui/dialog"
import {Label} from "@/components/ui/label"
import {Button} from "@/components/ui/button"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {Textarea} from "@/components/ui/textarea"
import {RadioGroup, RadioGroupItem} from "@/components/ui/radio-group"
import {Eye, FileDown} from "lucide-react"
import * as XLSX from 'xlsx'
import {VacationSchedulesAgenda} from "./vacation-schedules-agenda"
import {SpecialScheduleDayAgenda} from "./special-schedule-day-agenda"
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {AppointmentState} from "@/types/professional-schedules";
import {PatientResponse} from "@/types/patient/patientResponse";
import {cancelAppointment, cancelAppointments} from "@/app/api/utils/appointment/SlotApiUtils";

// Helper function to format date from YYYY-MM-DD to DD/MM/YYYY
const formatDate = (dateStr: string): string => {
    const [year, month, day] = dateStr.split('-')
    return `${day}/${month}/${year}`
}

interface AgendaOptionsDialogProps {
    isOpen: boolean
    onClose: () => void
    appointments: Record<string, ProfessionalAppointment[]>
    doctorInfo: DoctorsForMedicalCenter,
    patients: PatientResponse[],
    onUpdateWorkingDays: (
        daysOff: Date[],
        extraordinaryDay?: {
            date: Date;
            hours: { start: string; end: string }[]
        },
        daysOffToRemove?: Date[]
    ) => void,
    refetchSchedulesForMonth: (date: Date) => Promise<void>
    employeeUserId: number,
}

export function AgendaOptionsDialog({
                                        isOpen,
                                        onClose,
                                        appointments,
                                        doctorInfo,
                                        patients,
                                        onUpdateWorkingDays,
                                        refetchSchedulesForMonth,
                                        employeeUserId
                                    }: AgendaOptionsDialogProps) {
    const [activeTab, setActiveTab] = useState<"daysOff" | "extraordinary">("daysOff")
    // Track if components should be rendered - this helps with smooth transitions
    const [renderDaysOff, setRenderDaysOff] = useState<boolean>(true)
    const [renderExtraordinary, setRenderExtraordinary] = useState<boolean>(false)
    const [showWarning, setShowWarning] = useState<string | null>(null)
    const [warningAction, setWarningAction] = useState<"daysOff" | "extraordinary" | null>(null)
    const [affectedPatients, setAffectedPatients] = useState<PatientResponse[]>([])
    const [appointmentsToCancel, setAppointmentsToCancel] = useState<ProfessionalAppointment[]>([])
    const [cancellationOption, setCancellationOption] = useState<"auto" | "manual" | null>(null)
    const [cancellationReason, setCancellationReason] = useState<string>("")
    const [showAffectedAppointments, setShowAffectedAppointments] = useState<boolean>(false)
    const [extraordinaryDate, setExtraordinaryDate] = useState<Date>(new Date())
    const [extraordinaryHours, setExtraordinaryHours] = useState<{ start: string; end: string }[]>([{
        start: "08:00",
        end: "12:00"
    }])
    const [selectedDaysOff, setSelectedDaysOff] = useState<Date[]>([])
    const patientsByIdMap: Record<number, PatientResponse> = useMemo(() => {
        const map: Record<number, PatientResponse> = {}
        patients.forEach(patient => {
                map[patient.id] = patient
            }
        )
        return map
    }, [patients])


    // Initialize render states when dialog opens or closes
    useEffect(() => {
        if (isOpen) {
            // When dialog opens, set render states based on active tab
            setRenderDaysOff(activeTab === "daysOff");
            setRenderExtraordinary(activeTab === "extraordinary");
        } else {
            // Reset to default when dialog closes
            setActiveTab("daysOff");
            setRenderDaysOff(true);
            setRenderExtraordinary(false);
        }
    }, [isOpen, activeTab]);

    const hasAppointmentsOnDays = (days: Date[]) => {
        return days.some(date => {
            const dateStr = date.toISOString().split("T")[0];
            const apts = appointments[dateStr]?.filter(apt => apt.state !== AppointmentState.CANCELLED) || []
            return apts.length > 0
        })
    }

    const hasAppointmentsOutsideAllRanges = (date: Date, hours: { start: string; end: string }[]) => {
        const dateStr = date.toISOString().split("T")[0]
        const existingAppointments = appointments[dateStr]?.filter(
            apt => apt.state !== AppointmentState.CANCELLED
        ) || []

        return existingAppointments.some(apt => {
            return hours.every(range => !apt.isInRange(range.start, range.end, doctorInfo.appointmentDuration))
        })
    }

    const handleDaysOffSave = (daysToDisable: Date[], daysToRemove: Date[]) => {
        setSelectedDaysOff(daysToDisable)
        if (hasAppointmentsOnDays(daysToDisable)) {
            const aptsToCancel: ProfessionalAppointment[] = []
            daysToDisable.forEach(date => {
                const dateStr = date.toISOString().split("T")[0];
                const apts = appointments[dateStr]?.filter(apt => apt.state !== AppointmentState.CANCELLED) || []
                aptsToCancel.push(...apts)
            })

            // Sort appointments by date and time (closest to furthest)
            const sortedAppointments = [...aptsToCancel].sort((a, b) => {
                // Compare dates first
                const dateA = new Date(`${a.date}T${a.startTime}`)
                const dateB = new Date(`${b.date}T${b.startTime}`)
                return dateA.getTime() - dateB.getTime()
            })

            setAppointmentsToCancel(sortedAppointments)
            setShowWarning(`Se cancelarán ${sortedAppointments.length} turnos en las fechas seleccionadas.`)
            setWarningAction("daysOff")
        } else {
            onUpdateWorkingDays(daysToDisable, undefined, daysToRemove)
            onClose()
        }
    }

    const handleExtraordinarySave = (date: Date, hours: { start: string; end: string }[]) => {
        setExtraordinaryDate(date)
        setExtraordinaryHours(hours)
        const dateStr = date.toISOString().split("T")[0]
        if (appointments[dateStr] && hasAppointmentsOutsideAllRanges(date, hours)) {
            setShowWarning("Hay turnos fuera de los nuevos rangos horarios.")
            setWarningAction("extraordinary")
        } else {
            onUpdateWorkingDays([], {date: date, hours: hours})
            onClose()
        }
    }


    const handleCancellationOptionChange = (value: "auto" | "manual") => {
        setCancellationOption(value)
    }

    const handleWarningConfirm = () => {
        if (!cancellationOption) {
            return
        }

        if (warningAction === "daysOff") {
            if (cancellationOption === "auto") {
                cancelAppointments(appointmentsToCancel.map(apt => apt.id), employeeUserId, true)
                onUpdateWorkingDays(selectedDaysOff, undefined, [])
                onClose()
            } else if (cancellationOption === "manual") {
                // Cancel appointments but show the list for manual notification
                const affected = new Set<PatientResponse>()
                selectedDaysOff.forEach(date => {
                    const dateStr = date.toISOString().split("T")[0]
                    const apts = appointments[dateStr] || []
                    apts.forEach(apt => {
                        const patient = patientsByIdMap[apt.patientId]
                        if (patient) affected.add(patient)
                    })
                })
                setAffectedPatients(Array.from(affected))
                cancelAppointments(appointmentsToCancel.map(apt => apt.id), employeeUserId, false)
                appointmentsToCancel.forEach(apt => cancelAppointment(apt.id, employeeUserId))
                onUpdateWorkingDays(selectedDaysOff, undefined, [])
                onClose()
            }
        } else if (warningAction === "extraordinary") {
            const date = extraordinaryDate
            const dateStr = date.toISOString().split("T")[0]
            if (cancellationOption === "auto") {
                cancelAppointments(appointmentsToCancel.map(apt => apt.id), employeeUserId, true)
                onUpdateWorkingDays([], {date: date, hours: extraordinaryHours})
                onClose()
            } else if (cancellationOption === "manual") {
                // Show the list of affected appointments for manual notification
                const affected = new Set<PatientResponse>()
                appointments[dateStr]
                    ?.filter(apt =>
                        extraordinaryHours.every(range => !apt.isInRange(range.start, range.end, doctorInfo.appointmentDuration))
                    )
                    .forEach(apt => {
                        const patient = patientsByIdMap[apt.patientId]
                        if (patient) affected.add(patient)
                    })
                setAffectedPatients(Array.from(affected))
                cancelAppointments(appointmentsToCancel.map(apt => apt.id), employeeUserId, false)
                onUpdateWorkingDays([], {date: date, hours: extraordinaryHours})
                // Close the main dialog after setting affected patients
                onClose()
            }
        }

        // Reset state
        setShowWarning(null)
        setWarningAction(null)
        setAppointmentsToCancel([])
        setCancellationOption(null)
        setCancellationReason("")
    }

    const handleCancelWarning = () => {
        setShowWarning(null)
        setWarningAction(null)
        setAppointmentsToCancel([])
        setCancellationOption(null)
        setCancellationReason("")
        setShowAffectedAppointments(false)
    }

    const exportToExcel = (appointments: ProfessionalAppointment[], filename: string) => {
        // Create worksheet data
        const headers = ["Paciente", "Fecha", "Hora", "Contacto", "Cobertura"]


        const data = appointments.map(apt => [
            apt.patientName,
            formatDate(apt.date),              // Format date as DD/MM/YYYY
            apt.startTime,
            patientsByIdMap[apt.patientId]?.phone || "No disponible",
            apt.healthInsuranceInformation
        ])

        // Add headers to the beginning of the data array
        const wsData = [headers, ...data]

        // Create a worksheet
        const ws = XLSX.utils.aoa_to_sheet(wsData)

        // Create a workbook
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, "Turnos Cancelados")

        // Generate the Excel file and trigger download
        XLSX.writeFile(wb, `${filename}_${new Date().toISOString().split("T")[0]}.xlsx`)
    }

    return (
        <>
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="max-w-[600px] w-[90vw] transition-all duration-300 ease-in-out">
                    <DialogTitle className="text-center">Opciones de Agenda</DialogTitle>
                    <div className="flex justify-center">
                        <div className="inline-flex rounded-md shadow-md border border-gray-200" role="group">
                            <button
                                type="button"
                                className={`px-4 py-2 text-sm font-medium rounded-l-lg transition-all duration-200 ${
                                    activeTab === "daysOff"
                                        ? "bg-blue-600 text-white shadow-inner"
                                        : "bg-white text-gray-700 hover:bg-gray-50"
                                }`}
                                onClick={() => {
                                    setActiveTab("daysOff");
                                    // Ensure the extraordinary component is rendered for smooth transition
                                    setRenderExtraordinary(true);
                                    // After transition completes, we can stop rendering the inactive component
                                    setTimeout(() => {
                                        setRenderExtraordinary(false);
                                    }, 300);
                                }}
                            >
                                Días sin agenda
                            </button>
                            <button
                                type="button"
                                className={`px-4 py-2 text-sm font-medium rounded-r-lg transition-all duration-200 ${
                                    activeTab === "extraordinary"
                                        ? "bg-blue-600 text-white shadow-inner"
                                        : "bg-white text-gray-700 hover:bg-gray-50"
                                }`}
                                onClick={() => {
                                    setActiveTab("extraordinary");
                                    // Ensure the days off component is rendered for smooth transition
                                    setRenderDaysOff(true);
                                    // After transition completes, we can stop rendering the inactive component
                                    setTimeout(() => {
                                        setRenderDaysOff(false);
                                    }, 300);
                                }}
                            >
                                Días de atención extraordinaria
                            </button>
                        </div>
                    </div>

                    <div className="py-4 min-h-[400px] relative">
                        {/* Only render DaysOffAgenda if it's active or in transition */}
                        {(activeTab === "daysOff" || renderDaysOff) && (
                            <div
                                className={`absolute inset-0 transition-opacity duration-300 ease-in-out ${
                                    activeTab === "daysOff" ? "opacity-100 z-10" : "opacity-0 z-0 pointer-events-none"
                                }`}
                            >
                                <VacationSchedulesAgenda
                                    appointments={appointments}
                                    doctorInfo={doctorInfo}
                                    refetchSchedulesForMonth={refetchSchedulesForMonth}
                                    onSave={handleDaysOffSave}
                                />
                            </div>
                        )}

                        {/* Only render ExtraordinaryDayAgenda if it's active or in transition */}
                        {(activeTab === "extraordinary" || renderExtraordinary) && (
                            <div
                                className={`absolute inset-0 transition-opacity duration-300 ease-in-out ${
                                    activeTab === "extraordinary" ? "opacity-100 z-10" : "opacity-0 z-0 pointer-events-none"
                                }`}
                            >
                                <SpecialScheduleDayAgenda
                                    appointments={appointments}
                                    doctorInfo={doctorInfo}
                                    refetchSchedulesForMonth={refetchSchedulesForMonth}
                                    onSave={handleExtraordinarySave}
                                />
                            </div>
                        )}
                    </div>


                </DialogContent>
            </Dialog>

            <AlertDialog open={!!showWarning} onOpenChange={handleCancelWarning}>
                <AlertDialogContent className="sm:max-w-[600px]">
                    <AlertDialogHeader>
                        <AlertDialogTitle>Advertencia</AlertDialogTitle>
                        <AlertDialogDescription>{showWarning}</AlertDialogDescription>
                    </AlertDialogHeader>
                    {warningAction === "daysOff" && appointmentsToCancel.length > 0 && (
                        <ul className="mt-2 max-h-[150px] overflow-y-auto">
                            {appointmentsToCancel.map((apt, index) => (
                                <li key={index} className="text-sm">
                                    {apt.patientName} - {formatDate(apt.date)} {apt.startTime}
                                </li>
                            ))}
                        </ul>
                    )}

                    <div className="py-4">
                        <RadioGroup value={cancellationOption || ""}
                                    onValueChange={(value) => handleCancellationOptionChange(value as "auto" | "manual")}>
                            <div className="flex items-start space-x-2 mb-4">
                                <RadioGroupItem value="auto" id="auto-cancel" className="mt-1"/>
                                <div className="grid gap-1.5">
                                    <Label htmlFor="auto-cancel" className="font-medium">Cancelar automáticamente todos
                                        los turnos afectados</Label>
                                    {cancellationOption === "auto" && (
                                        <div className="mt-2">
                                            <Label htmlFor="cancel-reason" className="text-sm text-gray-600 mb-1 block">Motivo
                                                de la cancelación (se enviará por email a los pacientes)</Label>
                                            <Textarea
                                                id="cancel-reason"
                                                placeholder="Ingrese el motivo de la cancelación"
                                                value={cancellationReason}
                                                onChange={(e) => setCancellationReason(e.target.value)}
                                                className="resize-none"
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="flex items-start space-x-2">
                                <RadioGroupItem value="manual" id="manual-cancel" className="mt-1"/>
                                <div className="grid gap-1.5">
                                    <Label htmlFor="manual-cancel" className="font-medium">Cancelar y notificar
                                        manualmente a los pacientes</Label>
                                    <p className="text-sm text-gray-600">Los turnos serán cancelados en el sistema, pero
                                        deberá notificar a los pacientes manualmente.</p>
                                    {cancellationOption === "manual" && (
                                        <div className="mt-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="flex items-center gap-1"
                                                onClick={() => setShowAffectedAppointments(true)}
                                            >
                                                <Eye className="h-4 w-4"/>
                                                Ver turnos afectados
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </RadioGroup>
                    </div>

                    <AlertDialogFooter>
                        <AlertDialogCancel onClick={handleCancelWarning}>Cancelar</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleWarningConfirm}
                            disabled={!cancellationOption || (cancellationOption === "auto" && !cancellationReason.trim())}
                        >
                            Confirmar
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            <Dialog open={affectedPatients.length > 0 || showAffectedAppointments} onOpenChange={() => {
                setAffectedPatients([])
                setShowAffectedAppointments(false)
            }}>
                <DialogContent className="max-w-3xl">
                    <DialogHeader className="text-center">
                        <DialogTitle>Pacientes afectados</DialogTitle>
                    </DialogHeader>
                    <div className="max-h-[400px] overflow-y-auto">
                        {showAffectedAppointments && warningAction === "daysOff" && (
                            <div className="mb-4">
                                <div className="bg-gray-50 p-3 rounded-md mb-3">
                                    <h3 className="font-medium mb-1">Turnos afectados por cambio de agenda</h3>
                                    <p className="text-sm text-gray-600">Estos turnos serán cancelados al confirmar los
                                        cambios.</p>
                                </div>
                                <div className="overflow-x-auto">
                                    <table className="w-full border-collapse">
                                        <thead>
                                        <tr className="bg-gray-100">
                                            <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Paciente</th>
                                            <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Fecha</th>
                                            <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Hora</th>
                                            <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Contacto</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {appointmentsToCancel.map((apt, index) => (
                                            <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                                                <td className="py-2 px-3 text-sm">{apt.patientName}</td>
                                                <td className="py-2 px-3 text-sm">{formatDate(apt.date)}</td>
                                                <td className="py-2 px-3 text-sm">{apt.startTime}</td>
                                                <td className="py-2 px-3 text-sm">{patientsByIdMap[apt.patientId]?.phone || "No disponible"}</td>
                                            </tr>
                                        ))}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="mt-4">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-1"
                                        onClick={() => exportToExcel(appointmentsToCancel, "turnos_cancelados")}
                                    >
                                        <FileDown className="h-4 w-4"/>
                                        Exportar a Excel
                                    </Button>
                                </div>
                            </div>
                        )}
                        {showAffectedAppointments && warningAction === "extraordinary" && (
                            <div className="mb-4">
                                <div className="bg-gray-50 p-3 rounded-md mb-3">
                                    <h3 className="font-medium mb-1">Turnos afectados por cambio de horario</h3>
                                    <p className="text-sm text-gray-600">Estos turnos serán cancelados al confirmar los
                                        cambios.</p>
                                </div>
                                <div className="overflow-x-auto">
                                    <table className="w-full border-collapse">
                                        <thead>
                                        <tr className="bg-gray-100">
                                            <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Paciente</th>
                                            <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Fecha</th>
                                            <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Hora</th>
                                            <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Contacto</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {(() => {
                                            const date = extraordinaryDate
                                            const dateStr = date.toISOString().split("T")[0]
                                            const affectedAppts = appointments[dateStr]?.filter(apt => {
                                                if (apt.state === AppointmentState.CANCELLED) return false;
                                                return extraordinaryHours.every(range => !apt.isInRange(range.start, range.end, doctorInfo.appointmentDuration));
                                            }) || [];

                                            // Sort appointments by time (closest to furthest)
                                            const sortedAppts = [...affectedAppts].sort((a, b) => {
                                                // Compare dates first
                                                const dateA = new Date(`${a.date}T${a.startTime}`)
                                                const dateB = new Date(`${b.date}T${b.startTime}`)
                                                return dateA.getTime() - dateB.getTime()
                                            })


                                            return sortedAppts.map((apt, index) => (
                                                <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                                                    <td className="py-2 px-3 text-sm">{apt.patientName}</td>
                                                    <td className="py-2 px-3 text-sm">{formatDate(apt.date)}</td>
                                                    <td className="py-2 px-3 text-sm">{apt.startTime}</td>
                                                    <td className="py-2 px-3 text-sm">{patientsByIdMap[apt.patientId]?.phone || "No disponible"}</td>
                                                </tr>
                                            ))
                                        })()}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="mt-4">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-1"
                                        onClick={() => {
                                            const date = extraordinaryDate
                                            const dateStr = date.toISOString().split("T")[0]
                                            const affectedAppts = appointments[dateStr]?.filter(apt => {
                                                if (apt.state === AppointmentState.CANCELLED) return false;

                                                // Check if appointment is outside ALL time ranges
                                                return extraordinaryHours.every(range => !apt.isInRange(range.start, range.end, doctorInfo.appointmentDuration));
                                            }) || [];

                                            // Sort appointments by time (closest to furthest)
                                            const sortedAppts = [...affectedAppts].sort((a, b) => {
                                                // Compare dates first
                                                const dateA = new Date(`${a.date}T${a.startTime}`)
                                                const dateB = new Date(`${b.date}T${b.startTime}`)
                                                return dateA.getTime() - dateB.getTime()
                                            })


                                            exportToExcel(sortedAppts, "turnos_cancelados")
                                        }}
                                    >
                                        <FileDown className="h-4 w-4"/>
                                        Exportar a Excel
                                    </Button>
                                </div>
                            </div>
                        )}
                        {affectedPatients.length > 0 && (
                            <div>
                                {affectedPatients.map((patient, index) => (
                                    <div key={index} className="py-2 border-b">
                                        <p><strong>Nombre:</strong> {patient.name}</p>
                                        <p><strong>Teléfono:</strong> {patient.phone || "No disponible"}</p>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => {
                                setAffectedPatients([])
                                setShowAffectedAppointments(false)
                            }}
                        >
                            Cerrar
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}