"use client"

import {useMemo, useState} from "react"
import {Label} from "@/components/ui/label"
import {Button} from "@/components/ui/button"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {ChevronLeft, ChevronRight, Clock, Plus, Trash} from "lucide-react"
import {isBefore, startOfDay} from "date-fns"
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {AppointmentState, isDateAvailable} from "@/types/professional-schedules";
import {getMonthYearFormatForAgendasFromDate} from "@/app/api/utils/professionalSchedules/ProfessionalSchedulesUtils";

interface SpecialScheduleDayAgendaProps {
    appointments: Record<string, ProfessionalAppointment[]>
    doctorInfo: DoctorsForMedicalCenter,
    refetchSchedulesForMonth: (date: Date) => Promise<void>
    onSave: (dateStr: Date, hours: { start: string; end: string }[]) => void
}

export function SpecialScheduleDayAgenda({
                                             appointments,
                                             doctorInfo,
                                             refetchSchedulesForMonth,
                                             onSave,
                                         }: SpecialScheduleDayAgendaProps) {

    const [extraordinaryDate, setExtraordinaryDate] = useState<Date>(new Date())

    const agenda = useMemo(() => {
            if (!doctorInfo.agendaByMonthAndYear[getMonthYearFormatForAgendasFromDate(extraordinaryDate)]) {
                refetchSchedulesForMonth(extraordinaryDate)
            }
            return doctorInfo.agendaByMonthAndYear[getMonthYearFormatForAgendasFromDate(extraordinaryDate)]
        },
        [extraordinaryDate, doctorInfo.agendaByMonthAndYear])

    const existingSpecialSchedules = useMemo(() => {
        return agenda.getSpecialSchedulesByDate(extraordinaryDate)
    }, [agenda, extraordinaryDate])

    const [extraordinaryHours, setExtraordinaryHours] = useState<{ start: string; end: string }[]>(
        existingSpecialSchedules.length > 0
            ? existingSpecialSchedules.map(schedule => ({start: schedule.startTime, end: schedule.endTime}))
            : [{start: "08:00", end: "12:00"}]
    )

    const isBeforeToday = (date: Date): boolean => {
        const today = startOfDay(new Date())
        return isBefore(date, today)
    }

    const generateTimeOptions = (appointmentDuration: number) => {
        const options = []
        for (let hour = 0; hour < 24; hour++) {
            for (let minute = 0; minute < 60; minute += appointmentDuration) {
                const hourStr = hour.toString().padStart(2, "0")
                const minuteStr = minute.toString().padStart(2, "0")
                options.push(`${hourStr}:${minuteStr}`)
            }
        }
        return options
    }

    const timeOptions = useMemo(() => generateTimeOptions(doctorInfo.appointmentDuration), [doctorInfo])

    const initialHours = useMemo(() => {
        if (existingSpecialSchedules.length != 0) {
            return existingSpecialSchedules.map(schedule => ({start: schedule.startTime, end: schedule.endTime}))
        }
        return []
    }, [existingSpecialSchedules])

    const hasAppointmentsOutsideAllRanges = (date: Date, hours: { start: string; end: string }[]) => {
        const dateStr = date.toISOString().split("T")[0]
        const existingAppointments = appointments[dateStr]?.filter(apt => apt.state !== AppointmentState.CANCELLED) || []
        return existingAppointments.some(apt =>
            hours.every(range => apt.isInRange(range.start, range.end, doctorInfo.appointmentDuration))
        )
    }


    const isExtraordinaryDayAlreadyScheduled = () => {
        if (agenda.getVacationScheduleByDate(extraordinaryDate)) {
            return true
        }
        return !!existingSpecialSchedules;

    }

    const hasChanges = () => {
        if (existingSpecialSchedules.length != 0) {
            if (extraordinaryHours.length !== initialHours.length) return true

            for (let i = 0; i < extraordinaryHours.length; i++) {
                if (extraordinaryHours[i].start !== initialHours[i].start ||
                    extraordinaryHours[i].end !== initialHours[i].end) {
                    return true
                }
            }
            return false
        }

        return extraordinaryHours.length > 0
    }

    const handleAddTimeRange = () => {
        const lastRange = extraordinaryHours[extraordinaryHours.length - 1]
        let newStart = "14:00"
        let newEnd = "18:00"

        // If the last range ends after 12:00, add a range in the evening
        if (lastRange && lastRange.end > "12:00") {
            newStart = "18:00"
            newEnd = "20:00"
        }

        // If the last range ends after 18:00, add a range early morning next day
        if (lastRange && lastRange.end > "18:00") {
            newStart = "08:00"
            newEnd = "12:00"
        }

        setExtraordinaryHours([...extraordinaryHours, {start: newStart, end: newEnd}])
    }

    const handleRemoveTimeRange = (index: number) => {
        // Don't allow removing the last time range
        if (extraordinaryHours.length <= 1) return

        const updatedHours = [...extraordinaryHours]
        updatedHours.splice(index, 1)
        setExtraordinaryHours(updatedHours)
    }

    const handleTimeChange = (index: number, field: 'start' | 'end', value: string) => {
        const updatedHours = [...extraordinaryHours]

        // Update the specified field
        updatedHours[index] = {
            ...updatedHours[index],
            [field]: value
        }

        // If start time is after or equal to end time, adjust end time
        if (field === 'start' && value >= updatedHours[index].end) {
            // Find the index of the selected time in the options array
            const startIndex = timeOptions.indexOf(value)

            // Calculate a new end time that's at least one hour later
            // First try to add 12 slots (1 hour) if possible
            const newEndIndex = Math.min(startIndex + 12, timeOptions.length - 1)

            // Set the new end time
            updatedHours[index].end = timeOptions[newEndIndex]
        }

        // If end time is before or equal to start time, adjust start time
        if (field === 'end' && value <= updatedHours[index].start) {
            // Find the index of the selected time in the options array
            const endIndex = timeOptions.indexOf(value)

            // Calculate a new start time that's at least one hour earlier
            // First try to subtract 12 slots (1 hour) if possible
            const newStartIndex = Math.max(endIndex - 12, 0)

            // Set the new start time
            updatedHours[index].start = timeOptions[newStartIndex]
        }

        // Sort the time ranges by start time
        const sortedHours = [...updatedHours].sort((a, b) => a.start.localeCompare(b.start))

        setExtraordinaryHours(sortedHours)
    }

    const handleExtraordinarySave = () => {
        onSave(extraordinaryDate, extraordinaryHours)
    }

    const renderCalendarDays = (currentDate: Date, selectedDates: Date[], onDateClick: (date: Date) => void) => {
        function selectedDatesIncludes(date: Date) {
            return selectedDates.some(d => d.toISOString() === date.toISOString())
        }

        const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
        const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
        const startingDay = (firstDay.getDay() + 6) % 7

        return [
            ...Array.from({length: startingDay}).map((_, i) => (
                <div key={`empty-${i}`} className="p-2"/>
            )),
            ...Array.from({length: daysInMonth}, (_, i) => {
                const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), i + 1)
                const dateStr = date.toISOString().split("T")[0]
                const hasAppointments = appointments[dateStr]?.some(apt => apt.state !== AppointmentState.CANCELLED) || false
                const isSelected = selectedDatesIncludes(date)
                const isDayOff = agenda.getVacationScheduleByDate(date) || false
                const isExtraordinary = agenda.getSpecialSchedulesByDate(date).length > 0
                const isAvailable = isDateAvailable(date, agenda)
                const isPastDate = isBeforeToday(date)

                const isDisabled = isPastDate

                return (
                    <button
                        key={i}
                        onClick={() => onDateClick(date)}
                        disabled={isDisabled}
                        className={`
              group w-8 h-8 p-0 rounded-full relative
              ${isPastDate ? "text-gray-300 cursor-not-allowed" :
                            !isAvailable ? "text-gray-300" : ""}
              ${isSelected ? "bg-blue-500 text-white" :
                            isDayOff ? "text-red-600 font-medium hover:bg-red-50" :
                                isExtraordinary ? "text-blue-600 font-medium hover:bg-blue-50" :
                                    isPastDate ? "" : "hover:bg-blue-50"}
              ${isDisabled ? "cursor-not-allowed" : ""}
            `}
                    >
                        <div className="w-full h-full flex items-center justify-center">{i + 1}</div>
                        {hasAppointments && (
                            <div
                                className={`
                  absolute left-1/2 -translate-x-1/2
                  w-[0.375rem] h-[0.375rem] rounded-full
                  transition-all duration-200
                  ${isSelected ? "-bottom-[0.625rem] bg-white" : "-bottom-[0.125rem] bg-blue-500 group-hover:-bottom-[0.625rem]"}
                `}
                            />
                        )}
                    </button>
                )
            })
        ]
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column - Calendar */}
            <div className="border rounded-lg p-4 bg-white border-gray-200 shadow-sm h-fit">
                <div className="flex items-center justify-between mb-4">
          <span className="text-sm font-medium">
            {extraordinaryDate.toLocaleString('es', {month: 'long', year: 'numeric'})}
          </span>
                    <div className="flex gap-2">
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setExtraordinaryDate(prev => {
                                const newDate = new Date(prev)
                                newDate.setMonth(newDate.getMonth() - 1)
                                return newDate
                            })}
                        >
                            <ChevronLeft className="h-4 w-4"/>
                        </Button>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setExtraordinaryDate(prev => {
                                const newDate = new Date(prev)
                                newDate.setMonth(newDate.getMonth() + 1)
                                return newDate
                            })}
                        >
                            <ChevronRight className="h-4 w-4"/>
                        </Button>
                    </div>
                </div>
                <div className="grid grid-cols-7 gap-2 text-sm mb-2">
                    {["L", "M", "M", "J", "V", "S", "D"].map((day, i) => (
                        <div key={i} className="flex justify-center items-center w-8 mx-auto font-medium">{day}</div>
                    ))}
                </div>
                <div className="grid grid-cols-7 gap-2 text-sm h-[250px]">
                    {renderCalendarDays(extraordinaryDate, [extraordinaryDate], (date) => {
                        if (isBeforeToday(date)) return;
                        setExtraordinaryDate(date);
                    })}
                </div>


            </div>

            {/* Right Column - Time Ranges and Save Button */}
            <div className="flex flex-col h-full">
                <div className="border rounded-lg p-4 bg-white border-gray-200 shadow-sm mb-4 flex-grow">
                    <div className="mb-4">
                        <Label className="text-base font-medium">Horarios de atención</Label>
                    </div>

                    <div className="space-y-3 max-h-[300px] overflow-y-auto pr-1 mb-4">
                        {extraordinaryHours.map((hour, index) => (
                            <div key={index}
                                 className="flex items-center justify-between bg-white rounded-md p-2 border border-gray-200 shadow-sm">
                                <div className="flex items-center gap-2">
                                    <Clock className="h-4 w-4 text-blue-500 ml-1"/>
                                    <div className="flex items-center gap-2">
                                        <Select
                                            value={hour.start}
                                            onValueChange={(value) => handleTimeChange(index, 'start', value)}
                                        >
                                            <SelectTrigger className="border-0 p-1 shadow-none h-8 w-[4.5rem]">
                                                <SelectValue placeholder="Inicio"/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                {timeOptions.map(time => (
                                                    <SelectItem key={time} value={time}>
                                                        {time}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <span className="text-gray-400">→</span>
                                        <Select
                                            value={hour.end}
                                            onValueChange={(value) => handleTimeChange(index, 'end', value)}
                                        >
                                            <SelectTrigger className="border-0 p-1 shadow-none h-8 w-[4.5rem]">
                                                <SelectValue placeholder="Fin"/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                {timeOptions.map(time => (
                                                    <SelectItem key={time} value={time}>
                                                        {time}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                {/* Only show remove button if there's more than one time range */}
                                {extraordinaryHours.length > 1 && (
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                                        onClick={() => handleRemoveTimeRange(index)}
                                    >
                                        <Trash className="h-4 w-4"/>
                                    </Button>
                                )}
                            </div>
                        ))}
                    </div>

                    {/* Add time range button moved below the ranges */}
                    <div className="flex justify-center">
                        <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1 text-blue-600 border-blue-200 w-full"
                            onClick={handleAddTimeRange}
                        >
                            <Plus className="h-3.5 w-3.5"/>
                            Agregar horario
                        </Button>
                    </div>
                </div>

                {/* Save Button */}
                <div className="flex justify-center mt-auto">
                    <Button
                        className="bg-blue-500 hover:bg-blue-600 w-full py-6"
                        onClick={handleExtraordinarySave}
                        disabled={!hasChanges()}
                    >
                        {isExtraordinaryDayAlreadyScheduled() ? "Modificar día extraordinario" : "Guardar día extraordinario"}
                    </Button>
                </div>
            </div>
        </div>
    )
}
