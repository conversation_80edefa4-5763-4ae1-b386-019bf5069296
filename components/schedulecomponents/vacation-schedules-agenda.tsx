"use client"

import {useMemo, useState} from "react"
import {But<PERSON>} from "@/components/ui/button"
import {ChevronLeft, ChevronRight} from "lucide-react"
import {isBefore, startOfDay} from "date-fns"
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {AppointmentState, isDateAvailable} from "@/types/professional-schedules";
import {getMonthYearFormatForAgendasFromDate} from "@/app/api/utils/professionalSchedules/ProfessionalSchedulesUtils";

interface VacationSchedulesAgendaProps {
    appointments: Record<string, ProfessionalAppointment[]>
    doctorInfo: DoctorsForMedicalCenter
    refetchSchedulesForMonth: (date: Date) => Promise<void>
    onSave: (selectedDaysOff: Date[], daysToRemove: Date[]) => void
}

export function VacationSchedulesAgenda({
                                            appointments,
                                            doctorInfo,
                                            refetchSchedulesForMonth,
                                            onSave,
                                        }: VacationSchedulesAgendaProps) {

    const [daysOffDate, setDaysOffDate] = useState<Date>(new Date())
    const agenda = useMemo(() => {
            if (!doctorInfo.agendaByMonthAndYear[getMonthYearFormatForAgendasFromDate(daysOffDate)]) {
                refetchSchedulesForMonth(daysOffDate)
            }
            return doctorInfo.agendaByMonthAndYear[getMonthYearFormatForAgendasFromDate(daysOffDate)]
        },
        [daysOffDate, doctorInfo.agendaByMonthAndYear])

    const initialDaysOff: Date[] = useMemo(() => {
        if (agenda.vacationSchedules.length == 0) {
            return []
        }

        return agenda.vacationSchedules.flatMap(vacation => vacation.getDates())
    }, [agenda.vacationSchedules])

    function initialDaysOffIncludes(date: Date) {
        return initialDaysOff.some(d => d.toISOString() === date.toISOString())
    }

    const [selectedDaysOff, setSelectedDaysOff] = useState<Date[]>(initialDaysOff)

    function selectedDaysOffIncludes(date: Date) {
        return selectedDaysOff.some(d => d.toISOString() === date.toISOString())
    }

    const [daysToRemove, setDaysToRemove] = useState<Date[]>([])

    function daysToRemoveIncludes(date: Date) {
        return daysToRemove.some(d => d.toISOString() === date.toISOString())
    }

    const hasChanges = () => {

        if (selectedDaysOff.length !== initialDaysOff.length) return true

        for (const day of selectedDaysOff) {
            if (!initialDaysOffIncludes(day)) return true
        }
        return daysToRemove.length > 0;
    }

    const hasAppointmentsOnDays = (days: Date[]) => {
        return days.some(date => {
            const dateStr = date.toISOString().split("T")[0]
            const apts = appointments[dateStr]?.filter(apt => apt.startTime !== AppointmentState.CANCELLED) || []
            return apts.length > 0
        })
    }

    const isBeforeToday = (date: Date): boolean => {
        const today = startOfDay(new Date())
        return isBefore(date, today)
    }

    const handleDaysOffSave = () => {
        const finalSelectedDaysOff = selectedDaysOff.filter(date => !daysToRemoveIncludes(date))
        onSave(finalSelectedDaysOff, daysToRemove)
    }

    const renderCalendarDays = (currentDate: Date, selectedDates: Date[], onDateClick: (date: Date) => void) => {
        function selectedDatesIncludes(date: Date) {
            return selectedDates.some(d => d.toISOString() === date.toISOString())
        }

        const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
        const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
        const startingDay = (firstDay.getDay() + 6) % 7

        return [
            ...Array.from({length: startingDay}).map((_, i) => (
                <div key={`empty-${i}`} className="p-2"/>
            )),
            ...Array.from({length: daysInMonth}, (_, i) => {
                const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), i + 1)
                const hasAppointments = hasAppointmentsOnDays([date])
                const isSelected = selectedDatesIncludes(date)
                const isBeingReinstated = daysToRemoveIncludes(date)
                const isPastDate = isBeforeToday(date)
                const isAvailable = isDateAvailable(date, agenda) || isBeingReinstated
                const isDisabled = (!isAvailable && !isSelected) || isPastDate
                return (
                    <button
                        key={i}
                        onClick={() => onDateClick(date)}
                        disabled={isDisabled}
                        className={`
              group w-8 h-8 p-0 rounded-full relative
              ${isPastDate ? "text-gray-300 cursor-not-allowed" :
                            !isAvailable && !isBeingReinstated ? "text-gray-300" : ""}
              ${isSelected ? "bg-red-500 text-white" :
                            isBeingReinstated ? "text-blue-800 font-medium" :
                                isPastDate ? "" : "hover:bg-blue-50"}
              ${isDisabled ? "cursor-not-allowed" : ""}
            `}
                    >
                        <div className="w-full h-full flex items-center justify-center">{i + 1}</div>
                        {hasAppointments && (
                            <div
                                className={`
                  absolute left-1/2 -translate-x-1/2
                  w-[0.375rem] h-[0.375rem] rounded-full
                  transition-all duration-200
                  ${isSelected ? "-bottom-[0.625rem] bg-white" : "-bottom-[0.125rem] bg-blue-500 group-hover:-bottom-[0.625rem]"}
                `}
                            />
                        )}
                    </button>
                )
            })
        ]
    }

    const handleDaysOffDateClick = (date: Date) => {
        if (isBeforeToday(date)) {
            return
        }
        const wasOriginallyOff = initialDaysOffIncludes(date)

        if (selectedDaysOffIncludes(date)) {

            setSelectedDaysOff(prev => prev.filter(d => d.toISOString() !== date.toISOString()))

            // If it was originally off, add it to daysToRemove
            if (wasOriginallyOff) {
                setDaysToRemove(prev => [...prev, date])
            }
        } else {
            // Adding a new day
            setSelectedDaysOff(prev => [...prev, date])

            // If it was originally off but marked for removal, remove it from daysToRemove
            if (wasOriginallyOff) {
                setDaysToRemove(prev => prev.filter(d => d.toISOString() !== d.toISOString()))
            }
        }
    }

    return (
        <div className="space-y-4 w-full transition-all duration-300 ease-in-out">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Left Column - Calendar */}
                <div className="border rounded-lg p-4 bg-white border-gray-200 shadow-sm h-fit">
                    <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium">
              {daysOffDate.toLocaleString('es', {month: 'long', year: 'numeric'})}
            </span>
                        <div className="flex gap-2">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setDaysOffDate(prev => {
                                    const newDate = new Date(prev)
                                    newDate.setMonth(newDate.getMonth() - 1)
                                    return newDate
                                })}
                            >
                                <ChevronLeft className="h-4 w-4"/>
                            </Button>
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setDaysOffDate(prev => {
                                    const newDate = new Date(prev)
                                    newDate.setMonth(newDate.getMonth() + 1)
                                    return newDate
                                })}
                            >
                                <ChevronRight className="h-4 w-4"/>
                            </Button>
                        </div>
                    </div>
                    <div className="grid grid-cols-7 gap-2 text-sm mb-2">
                        {["L", "M", "M", "J", "V", "S", "D"].map((day, i) => (
                            <div key={i}
                                 className="flex justify-center items-center w-8 mx-auto font-medium">{day}</div>
                        ))}
                    </div>
                    <div className="grid grid-cols-7 gap-2 text-sm h-[250px]">
                        {renderCalendarDays(daysOffDate, selectedDaysOff, handleDaysOffDateClick)}
                    </div>
                </div>

                {/* Right Column - Instructions and Save Button */}
                <div className="flex flex-col h-full">
                    <div className="border rounded-lg p-4 bg-white border-gray-200 shadow-sm mb-4 flex-grow">
                        <div className="mb-4">
                            <h3 className="text-base font-medium">Días sin agenda</h3>
                            <p className="text-sm text-gray-600 mt-2">
                                Seleccione los días en los que el profesional no atenderá.
                            </p>
                        </div>
                        <div className="mt-4 space-y-2">
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 rounded-full bg-red-500"></div>
                                <span className="text-sm">Día sin agenda</span>
                            </div>
                            <div className="flex items-center gap-2 mt-[5px]">
                                <div className="w-4 h-4 rounded-full bg-blue-50 border border-blue-200"></div>
                                <span className="text-sm">Día disponible</span>
                            </div>
                            <div className="flex items-center gap-2 mt-[5px]">
                                <div className="relative w-4 h-6">
                                    <div
                                        className="absolute top-1/2 -translate-y-1/2 left-0 w-4 h-4 rounded-full bg-white border border-gray-300"></div>
                                    <div
                                        className="absolute top-[1.55rem] left-1/2 -translate-x-1/2 w-[0.375rem] h-[0.375rem] rounded-full bg-blue-500"></div>
                                </div>
                                <span className="text-sm">Día con turnos</span>
                            </div>
                        </div>
                    </div>

                    {/* Save Button */}
                    <div className="flex justify-center mt-auto">
                        <Button
                            className="bg-blue-500 hover:bg-blue-600 w-full py-6"
                            onClick={handleDaysOffSave}
                            disabled={!hasChanges()}
                        >
                            Guardar días sin agenda
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}
