"use client"

import React, {use<PERSON><PERSON>back, use<PERSON>ontex<PERSON>, useEffect, useRef, useState} from "react"
import {<PERSON>ertCircle, AlertTriangle, ChevronDown, Info, Search, X} from "lucide-react"
import {But<PERSON>} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Label} from "@/components/ui/label"
import {Switch} from "@/components/ui/switch"
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from "@/components/ui/collapsible"
import {cn} from "@/lib/utils"
import {DEFAULT_COVERAGES} from "@/data/coverages"
import {PhoneInput} from "react-international-phone";
import "react-international-phone/style.css";
import "@/styles/phone-input.css";
import {getSpanishCountries} from "@/data/phoneCountries";
import {PhoneNumberUtil} from 'google-libphonenumber';
import {PatientDetailsDialog} from "@/components/schedulecomponents/patient-details-dialog";
import {ConsultationInfoTable, type ConsultationTypeInfo} from "@/components/schedulecomponents/ConsultationInfoTable";

interface CollapsibleSectionProps {
    title: string
    children: React.ReactNode
    isOpen: boolean
    onOpenChange: (open: boolean) => void
    summary?: string | React.ReactNode
    isAnySectionOpen?: boolean
    hasWarning?: boolean
    isComplete?: boolean
}

function CollapsibleSection({
                                title,
                                children,
                                isOpen,
                                onOpenChange,
                                summary,
                                isAnySectionOpen,
                                hasWarning,
                                isComplete
                            }: CollapsibleSectionProps) {
    const isGrayedOut = !isOpen && isAnySectionOpen;

    return (
        <Collapsible
            open={isOpen}
            onOpenChange={onOpenChange}
            className={cn(
                "w-full",
                isOpen && "border rounded-lg",
                isOpen && hasWarning && "border-yellow-400 bg-yellow-50",
                isOpen && !hasWarning && "border-gray-200"
            )}
        >
            <CollapsibleTrigger className={cn(
                "flex items-center justify-between w-full p-3",
                "transition-colors duration-200",
                !isOpen && "border rounded-lg",
                !isOpen && hasWarning && "border-yellow-400 bg-yellow-50 text-yellow-800 hover:bg-yellow-100",
                !isOpen && !hasWarning && isComplete && "border-green-400 bg-green-50 text-green-800 hover:bg-green-100",
                isGrayedOut && !hasWarning && !isComplete
                    ? "bg-gray-50 border-gray-100 text-gray-400 hover:bg-gray-100"
                    : !isOpen && !hasWarning && !isComplete ? "border-gray-200 hover:bg-gray-50" : "",
                isOpen && "rounded-b-none",
                isOpen && hasWarning && "hover:bg-yellow-100",
                isOpen && !hasWarning && "hover:bg-gray-50"
            )}>
                <div className="flex flex-col items-start text-left">
                    <div className="flex items-center gap-2">
                        {hasWarning && <AlertTriangle className="h-4 w-4 text-yellow-600 flex-shrink-0"/>}
                        <h3 className={cn("text-sm font-medium", isGrayedOut ? "text-gray-500" : "", hasWarning ? "text-yellow-900" : "", !isOpen && !hasWarning && isComplete ? "text-green-900" : "")}>{title}</h3>
                    </div>
                    {!isOpen && summary && (
                        <span
                            className={cn("text-sm break-words max-w-full mt-1 pl-0", isGrayedOut ? "text-gray-400" : "", hasWarning ? "text-yellow-700" : isComplete ? "text-green-700" : "text-gray-500")}>{summary}</span>
                    )}
                </div>
                <ChevronDown className={cn(
                    "h-4 w-4 transition-transform ml-4 flex-shrink-0",
                    isOpen ? "transform rotate-180" : "",
                    isGrayedOut ? "text-gray-400" : "",
                    hasWarning ? "text-yellow-600" : isComplete && !isOpen ? "text-green-600" : ""
                )}/>
            </CollapsibleTrigger>
            <CollapsibleContent className={cn(
                "space-y-4 transition-all pt-4 px-3 pb-3",
                isOpen ? "" : "h-0 overflow-hidden",
                hasWarning && "border-t border-yellow-300"
            )}>
                {children}
            </CollapsibleContent>
        </Collapsible>
    )
}

const normalizeString = (str: string) => {
    return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "")
}

const dropdownItemStyle = "p-[0.375rem] text-[0.875rem] hover:bg-gray-100 transition-colors cursor-pointer"
const dropdownButtonStyle = "w-full text-left px-[0.5rem] py-[0.375rem] text-[0.875rem] hover:bg-gray-100 border-t"

// Using Patient type from types/patient.ts

const capitalizeName = (name: string) => {
    return name
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(" ")
}

export interface AppointmentData {
    patient: {
        id: string
        name: string
        dni: string
        coverage: string
        phone: string
    } | null
    time: string
    type: string
    duration: number
    selected_coverage: number | null
}

interface ConsultationType {
    name: string;
    duration: string; // e.g., "default" or "2slots"
    basePrice: number; // Added from your full ConsultationType definition
}

interface NewAppointmentFormProps {
    onCancel: () => void
    onConfirm: (data: AppointmentData) => void
    availableSlots: string[]
    selectedTime?: string | null
    selectedDate: Date
    timeSlots: string[]
    doctorId: number
    appointmentDuration?: number;
    medicalCenterId?: number;
    acceptedCoverages: string[];
}

export function NewAppointmentForm({
                                       onCancel,
                                       onConfirm,
                                       availableSlots,
                                       selectedTime,
                                       selectedDate,
                                       timeSlots,
                                       doctorId,
                                       appointmentDuration,
                                       medicalCenterId,
                                       acceptedCoverages,
                                   }: NewAppointmentFormProps) {
    const {editedConfig, doctors, refreshDoctorsFromStorage} = useContext(DoctorContext)
    const {appointments, isSlotBlocked} = useAppointments()
    const {patients, addPatient, searchPatients, getPatientsByMedicalCenter} = usePatients()
    // Removed unused auth variables since we're not checking for existing patients anymore
    const {isDoctorCoverageExcluded, isConsultationTypeExcluded} = useContext(CoverageContext)

    // Refresh doctors from storage when component mounts or doctorId changes
    useEffect(() => {
        refreshDoctorsFromStorage();
    }, [refreshDoctorsFromStorage, doctorId]);

    const currentDoctor = doctors.find((d) => d.id === doctorId)

    const effectiveConfig = React.useMemo(() => {
        return editedConfig && editedConfig.id === doctorId
            ? editedConfig
            : currentDoctor || {
            consultationTypes: [],
            appointmentSlotDuration: 15,
            name: "",
            id: "",
            specialties: [],
            workingDays: {},
            mn: "",
            onlineBookingAdvanceDays: 60,
            onlineBookingMinHours: 2
        };
    }, [editedConfig, doctorId, currentDoctor]);

    const doctorConsultationTypes = React.useMemo(() =>
            effectiveConfig?.consultationTypes || [],
        [effectiveConfig]
    )

    // Add state for collapsible sections
    const [openSections, setOpenSections] = useState({
        schedule: !selectedTime, // Only open schedule if no time is selected
        patient: !!selectedTime, // Open patient section if time is pre-selected
        coverage: false,
        consultationType: false,
    })

    // Function to handle section toggle
    const toggleSection = (section: keyof typeof openSections) => {
        setOpenSections((prev) => {
            const newState = {...prev}
            // Close all sections
            Object.keys(newState).forEach((key) => {
                newState[key as keyof typeof openSections] = false
            })
            // Open the clicked section
            newState[section] = !prev[section]
            return newState
        })
    }

    const [formData, setFormData] = useState<AppointmentData>({
        patient: null,
        time: selectedTime || "",
        type: "",
        duration: appointmentDuration || 15,
        selected_coverage: null
    })
    const [searchTerm, setSearchTerm] = useState("")
    const [dropdownOpen, setDropdownOpen] = useState(false)
    const [isCreatingNewPatient, setIsCreatingNewPatient] = useState(false)
    const [newPatientData, setNewPatientData] = useState({
        name: "",
        lastName: "",
        coverage: "",
        plan: "",
        noCoverage: false,
        dni: "",
        phone: "+54", // Default to Argentina with country code
        email: "",
    })
    const [phoneError, setPhoneError] = useState<string>("");

    // Initialize Google's libphonenumber utility
    const phoneUtil = PhoneNumberUtil.getInstance();
    // State for handling default coverage
    const [hasDefaultCoverage, setHasDefaultCoverage] = useState(false)
    const [useDefaultCoverage, setUseDefaultCoverage] = useState(true)
    const [selectedPatientCoverage, setSelectedPatientCoverage] = useState("") // Store the patient's coverage
    const [overrideCoverage, setOverrideCoverage] = useState("") // For when overriding default coverage
    const [overridePlan, setOverridePlan] = useState("") // For when overriding default coverage
    const [overrideNoCoverage, setOverrideNoCoverage] = useState(false) // For when overriding with no coverage
    const [timeslotError, setTimeslotError] = useState("")
    const [consultationSearch, setConsultationSearch] = useState("")
    const [showConsultationDropdown, setShowConsultationDropdown] = useState(false)
    const [selectedTypes, setSelectedTypes] = useState<string[]>([])
    const [consultationTypeConfirmed, setConsultationTypeConfirmed] = useState(false)
    const [showAllConsultationsModal, setShowAllConsultationsModal] = useState(false)
    const [isCoverageOpen, setIsCoverageOpen] = useState(false)
    const [isPlanOpen, setIsPlanOpen] = useState(false)
    const [isCoverageAccepted, setIsCoverageAccepted] = useState<boolean | null>(null);
    const [showPatientDetails, setShowPatientDetails] = useState(false);
    const [selectedPatientForDetails, setSelectedPatientForDetails] = useState<Patient | null>(null);
    const [showInstructionsDialog, setShowInstructionsDialog] = useState(false);

    const patientSearchRef = useRef<HTMLDivElement>(null)
    const consultationRef = useRef<HTMLDivElement>(null)
    const patientInputRef = useRef<HTMLInputElement>(null)
    const consultationInputRef = useRef<HTMLInputElement>(null)

    // Dynamically create plansByCoverage from DEFAULT_COVERAGES
    const plansByCoverage = React.useMemo(() => {
        return DEFAULT_COVERAGES.reduce((acc, coverage) => {
            acc[coverage.name] = coverage.plans || [];
            return acc;
        }, {} as Record<string, string[]>);
    }, []);

    const getDuration = (type: ConsultationType) => {
        const slotDuration = effectiveConfig.appointmentSlotDuration || 15;
        const multiplier = type.duration === "default" ? 1 : (isNaN(parseInt(type.duration)) ? 1 : parseInt(type.duration));
        return slotDuration * multiplier;
    };

    // Using ConsultationTypeInfo from our shared component

    // State to store information for all selected consultation types
    const [consultationTypesInfo, setConsultationTypesInfo] = useState<ConsultationTypeInfo[]>([]);

    // Function to show consultation type instructions and requirements
    const showConsultationInfo = useCallback((typeNames?: string[]) => {
        // If no type names provided, use all selected types
        const typesToShow = typeNames || selectedTypes;

        if (typesToShow.length === 0) return;

        const typesInfo: ConsultationTypeInfo[] = [];
        let hasAnyInfo = false;

        // Process each consultation type
        for (const typeName of typesToShow) {
            const consultationType = doctorConsultationTypes.find(t => t.name === typeName);
            if (!consultationType) continue;

            // Check if there's any info to show
            const hasInstructions = consultationType.hasInstructions && consultationType.instructions;
            const requiresOrder = consultationType.requiresMedicalOrder;

            // Get current coverage information
            let coverageId = "";
            let planId: string | null = null;
            let isExcluded = false;
            let copay = null;

            if (formData.patient) {
                // Determine which coverage to check
                let coverageToCheck = "";

                if (useDefaultCoverage && hasDefaultCoverage) {
                    coverageToCheck = selectedPatientCoverage;
                } else if (overrideNoCoverage) {
                    coverageToCheck = "Sin Cobertura";
                } else if (overrideCoverage) {
                    coverageToCheck = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
                } else if (formData.patient.coverage) {
                    coverageToCheck = formData.patient.coverage;
                }

                // If coverage is not selected, don't show coverage-related info
                if (coverageToCheck === "" || coverageToCheck === "No seleccionado") {
                    coverageToCheck = "";
                }

                // Find the coverage in DEFAULT_COVERAGES
                if (coverageToCheck === "Sin Cobertura") {
                    // Handle Sin Cobertura
                    const sinCobertura = DEFAULT_COVERAGES.find(c => c.name === "Sin Cobertura");
                    if (sinCobertura) {
                        coverageId = sinCobertura.id;

                        // Check if this consultation type accepts private pay
                        if (consultationType.acceptsPrivatePay === false) {
                            isExcluded = true;
                        }
                    }
                } else if (coverageToCheck) {
                    // Extract base coverage name and plan
                    const foundCoverage = DEFAULT_COVERAGES.find(cov => coverageToCheck.startsWith(cov.name));
                    if (foundCoverage) {
                        coverageId = foundCoverage.id;

                        // Extract plan if present
                        if (coverageToCheck !== foundCoverage.name) {
                            const planPart = coverageToCheck.substring(foundCoverage.name.length).trim();
                            if (planPart && foundCoverage.plans.includes(planPart)) {
                                planId = planPart;
                            }
                        }

                        // Check if this coverage is excluded for this consultation type
                        isExcluded = isConsultationTypeExcluded(consultationType, coverageId, planId);

                        // Check if there's a copay for this coverage
                        const foundCopay = consultationType.copays?.find(
                            c => c.coverageId === coverageId &&
                                (c.planId === null || c.planId === planId)
                        );

                        if (foundCopay) {
                            copay = foundCopay.amount;
                        }
                    }
                }
            }

            // Add to types info array
            typesInfo.push({
                name: typeName,
                requiresMedicalOrder: requiresOrder,
                instructions: hasInstructions ? consultationType.instructions || "" : "",
                isExcluded: isExcluded,
                copayAmount: copay,
                basePrice: consultationType.basePrice || 0,
                acceptsPrivatePay: consultationType.acceptsPrivatePay !== false, // Default to true if not specified
            });

            // Check if there's any info to show
            if (requiresOrder || hasInstructions || isExcluded || copay !== null) {
                hasAnyInfo = true;
            }
        }

        // Only show dialog if there's any info to display
        if (hasAnyInfo) {
            // No need to update individual state variables anymore
            // We're using the consultationTypesInfo array for all data

            // Store all types info for the table display
            setConsultationTypesInfo(typesInfo);
            setShowInstructionsDialog(true);
        }
    }, [
        doctorConsultationTypes,
        formData.patient,
        useDefaultCoverage,
        hasDefaultCoverage,
        selectedPatientCoverage,
        overrideNoCoverage,
        overrideCoverage,
        overridePlan,
        isConsultationTypeExcluded,
        selectedTypes
    ]);

    // Helper to check if a time slot is in the past (only allow previous appointment slot)
    const isPastTimeSlot = useCallback((time: string): boolean => {
        const now = new Date();
        const [hours, minutes] = time.split(':').map(Number);
        const slotDate = new Date(selectedDate);
        slotDate.setHours(hours, minutes, 0, 0);

        // Only show current time indicator for today
        if (selectedDate.toDateString() !== now.toDateString()) {
            // For dates other than today, all past slots are disabled
            return slotDate.getTime() < now.getTime();
        }

        // For today, find the current or next upcoming slot
        const currentTimeMinutes = now.getHours() * 60 + now.getMinutes();
        const slotTimeMinutes = hours * 60 + minutes;

        // Find the index of the current slot in the timeSlots array
        const currentSlotIndex = timeSlots.findIndex(slot => {
            const [slotHour, slotMinute] = slot.split(':').map(Number);
            const slotTime = slotHour * 60 + slotMinute;
            return slotTime >= currentTimeMinutes;
        });

        // If we found a current/upcoming slot, allow only the previous slot to be available
        if (currentSlotIndex > 0) {
            const timeSlotIndex = timeSlots.indexOf(time);
            // Allow only the immediately previous slot (currentSlotIndex - 1) and future slots
            return timeSlotIndex < currentSlotIndex - 1;
        } else if (currentSlotIndex === 0) {
            // If current time is before the first slot, no past slots to allow
            return slotTimeMinutes < currentTimeMinutes;
        } else {
            // If current time is after all slots, allow only the last slot
            const timeSlotIndex = timeSlots.indexOf(time);
            return timeSlotIndex < timeSlots.length - 1;
        }
    }, [selectedDate, timeSlots]);

    useEffect(() => {
        if (selectedTime) {
            // Only set the selected time if it's not in the past
            if (!isPastTimeSlot(selectedTime)) {
                setFormData((prev) => ({...prev, time: selectedTime}))

                // Determine which section to open next based on what's already filled
                if (formData.patient) {
                    // Open consultation type section since patient is already selected
                    setOpenSections({
                        schedule: false,
                        patient: false,
                        coverage: false,
                        consultationType: true
                    })
                } else {
                    // No patient selected yet, open patient section
                    setOpenSections({
                        schedule: false,
                        patient: true,
                        coverage: false,
                        consultationType: false
                    })
                }
            }
        }
    }, [selectedTime, selectedDate, isPastTimeSlot, formData.patient])

    // State to track if info popup has been shown for the current selection
    const [infoShownForSelection, setInfoShownForSelection] = useState<boolean>(false);

    // Effect to handle consultation type confirmation
    useEffect(() => {
        // Only run this effect when consultationTypeConfirmed changes to true
        if (consultationTypeConfirmed && selectedTypes.length > 0 && formData.patient) {
            // When consultation type is confirmed, collapse the consultation section
            // and expand the next relevant section or just collapse this one if all are filled
            setOpenSections(prev => ({
                ...prev,
                consultationType: false
            }))
        }
    }, [consultationTypeConfirmed, selectedTypes.length, formData.patient])

    // Reset infoShownForSelection when selected types change
    useEffect(() => {
        setInfoShownForSelection(false);
    }, [selectedTypes]);

    // Effect to check coverage acceptance when relevant state changes
    useEffect(() => {
        if (!formData.patient) {
            setIsCoverageAccepted(null); // No patient, no check needed
            return;
        }

        let coverageToCheck = "";
        let baseCoverageName = "";

        // Apply the same priority logic as in other functions
        if (useDefaultCoverage && hasDefaultCoverage) {
            coverageToCheck = selectedPatientCoverage;
        } else if (overrideNoCoverage) {
            coverageToCheck = "Sin Cobertura";
        } else if (overrideCoverage) {
            coverageToCheck = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
        } else if (formData.patient.coverage) { // Fallback if override isn't set but default isn't used
            coverageToCheck = formData.patient.coverage;
        }

        if (coverageToCheck === "Sin Cobertura") {
            baseCoverageName = "Sin Cobertura";
        } else if (coverageToCheck) {
            // Extract base name (e.g., "OSDE" from "OSDE 310")
            const foundCoverage = DEFAULT_COVERAGES.find(cov => coverageToCheck.startsWith(cov.name));
            baseCoverageName = foundCoverage ? foundCoverage.name : "";
        }

        if (baseCoverageName && doctorId) {
            // Find the coverage in DEFAULT_COVERAGES
            const foundCoverage = DEFAULT_COVERAGES.find(cov => cov.name === baseCoverageName);
            if (foundCoverage) {
                // Check if this specific plan is excluded
                let planId = null;
                if (coverageToCheck !== "Sin Cobertura" && coverageToCheck !== baseCoverageName) {
                    // Extract plan part (e.g., "310" from "OSDE 310")
                    const planPart = coverageToCheck.substring(baseCoverageName.length).trim();
                    if (planPart) {
                        const coveragePlans = foundCoverage.plans || [];
                        if (coveragePlans.includes(planPart)) {
                            planId = planPart;
                        }
                    }
                }

                // First check if the base coverage is excluded
                const isBaseCoverageExcluded = isDoctorCoverageExcluded(doctorId, foundCoverage.id);

                if (isBaseCoverageExcluded) {
                    // If the base coverage is excluded, all plans are excluded
                    setIsCoverageAccepted(false);
                } else if (planId) {
                    // If base coverage is accepted but we have a specific plan, check that plan
                    const isPlanExcluded = isDoctorCoverageExcluded(doctorId, foundCoverage.id, planId);
                    setIsCoverageAccepted(!isPlanExcluded);
                } else {
                    // Base coverage is accepted and no specific plan
                    setIsCoverageAccepted(true);
                }
            } else {
                setIsCoverageAccepted(null); // Coverage not found in system
            }
        } else {
            setIsCoverageAccepted(null); // Cannot determine or no coverage selected
        }

    }, [
        formData.patient,
        useDefaultCoverage,
        hasDefaultCoverage,
        selectedPatientCoverage,
        overrideCoverage,
        overridePlan,
        overrideNoCoverage,
        acceptedCoverages,
        doctorId,
        isDoctorCoverageExcluded
    ]);

    // Effect to update coverage acceptance state when consultation types or coverage changes
    // This effect no longer shows the popup automatically - that happens only after confirmation
    useEffect(() => {
        // Skip if no patient or no selected types
        if (!formData.patient || selectedTypes.length === 0) {
            return;
        }

        // Check if any selected consultation type has coverage exclusions
        const updateCoverageAcceptance = () => {
            // Only proceed if we have selected types and a patient with coverage
            if (selectedTypes.length > 0 && formData.patient) {
                // Get current coverage information
                let coverageToCheck = "";

                if (useDefaultCoverage && hasDefaultCoverage) {
                    coverageToCheck = selectedPatientCoverage;
                } else if (overrideNoCoverage) {
                    coverageToCheck = "Sin Cobertura";
                } else if (overrideCoverage) {
                    // Only include plan if it's selected
                    coverageToCheck = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
                } else if (formData.patient.coverage) {
                    coverageToCheck = formData.patient.coverage;
                }

                // If no coverage is selected or it's "No seleccionado", don't proceed
                if (!coverageToCheck || coverageToCheck === "No seleccionado") {
                    return;
                }

                // If a coverage is selected but needs a plan and no plan is selected, don't proceed
                if (overrideCoverage && !overrideNoCoverage && !overridePlan) {
                    // Check if this coverage has plans
                    const foundCoverage = DEFAULT_COVERAGES.find(cov => cov.name === overrideCoverage);
                    if (foundCoverage && foundCoverage.plans && foundCoverage.plans.length > 0) {
                        return; // Don't proceed until plan is selected
                    }
                }

                // Get the first selected consultation type
                const typeName = selectedTypes[0];
                const consultationType = doctorConsultationTypes.find(t => t.name === typeName);

                if (consultationType) {
                    // We'll check for exclusions when showing the dialog in the table display
                }
            }
        };

        // Update coverage acceptance state
        updateCoverageAcceptance();
    }, [
        // Include all required dependencies
        formData.patient,
        selectedTypes,
        doctorConsultationTypes,
        useDefaultCoverage,
        hasDefaultCoverage,
        selectedPatientCoverage,
        overrideCoverage,
        overridePlan,
        overrideNoCoverage,
        isConsultationTypeExcluded
    ]);

    // Effect to check if selected consultation types have coverage exclusions
    useEffect(() => {
        // Skip if no patient or no selected types
        if (!formData.patient || selectedTypes.length === 0) {
            return;
        }

        // Get current coverage information
        let coverageToCheck = "";

        if (useDefaultCoverage && hasDefaultCoverage) {
            coverageToCheck = selectedPatientCoverage;
        } else if (overrideNoCoverage) {
            coverageToCheck = "Sin Cobertura";
        } else if (overrideCoverage) {
            coverageToCheck = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
        } else if (formData.patient.coverage) {
            coverageToCheck = formData.patient.coverage;
        }

        if (!coverageToCheck) {
            return; // No coverage to check
        }

        // Check if any selected consultation type has an exclusion for this coverage
        let hasExclusion = false;

        if (coverageToCheck === "Sin Cobertura") {
            // For Sin Cobertura, check if any selected type doesn't accept private pay
            const sinCobertura = DEFAULT_COVERAGES.find(c => c.name === "Sin Cobertura");
            if (sinCobertura) {
                hasExclusion = selectedTypes.some(typeName => {
                    const consultationType = doctorConsultationTypes.find(t => t.name === typeName);
                    return consultationType && isConsultationTypeExcluded(consultationType, sinCobertura.id);
                });
            }
        } else {
            // For other coverages, check for explicit exclusions
            const foundCoverage = DEFAULT_COVERAGES.find(cov => coverageToCheck.startsWith(cov.name));
            if (foundCoverage) {
                const coverageId = foundCoverage.id;

                // Extract plan if present
                let planId: string | null = null;
                if (coverageToCheck !== foundCoverage.name) {
                    const planPart = coverageToCheck.substring(foundCoverage.name.length).trim();
                    if (planPart && foundCoverage.plans.includes(planPart)) {
                        planId = planPart;
                    }
                }

                // Check if any selected consultation type has an exclusion for this coverage
                hasExclusion = selectedTypes.some(typeName => {
                    const consultationType = doctorConsultationTypes.find(t => t.name === typeName);
                    return consultationType && isConsultationTypeExcluded(consultationType, coverageId, planId);
                });
            }
        }

        // Update coverage acceptance state if any exclusion is found
        if (hasExclusion) {
            setIsCoverageAccepted(false);
        } else if (isCoverageAccepted === false) {
            // If previously marked as not accepted due to consultation type, but now all types accept it,
            // we need to recheck the general coverage acceptance
            if (coverageToCheck === "Sin Cobertura") {
                // For Sin Cobertura, check if it's excluded for the doctor
                const sinCobertura = DEFAULT_COVERAGES.find(c => c.name === "Sin Cobertura");
                if (sinCobertura) {
                    const isExcluded = isDoctorCoverageExcluded(doctorId, sinCobertura.id);
                    setIsCoverageAccepted(!isExcluded);
                }
            } else {
                // For other coverages, check both base coverage and plan
                const foundCoverage = DEFAULT_COVERAGES.find(cov => coverageToCheck.startsWith(cov.name));
                if (foundCoverage) {
                    // First check if the base coverage is excluded
                    const isBaseCoverageExcluded = isDoctorCoverageExcluded(doctorId, foundCoverage.id);

                    if (isBaseCoverageExcluded) {
                        // If the base coverage is excluded, all plans are excluded
                        setIsCoverageAccepted(false);
                    } else {
                        // If base coverage is accepted, check if the specific plan is excluded
                        let planId = null;
                        if (coverageToCheck !== foundCoverage.name) {
                            const planPart = coverageToCheck.substring(foundCoverage.name.length).trim();
                            if (planPart && foundCoverage.plans.includes(planPart)) {
                                planId = planPart;
                            }
                        }

                        if (planId) {
                            const isPlanExcluded = isDoctorCoverageExcluded(doctorId, foundCoverage.id, planId);
                            setIsCoverageAccepted(!isPlanExcluded);
                        } else {
                            // Base coverage is accepted and no specific plan
                            setIsCoverageAccepted(true);
                        }
                    }
                }
            }
        }
    }, [
        formData.patient,
        selectedTypes,
        useDefaultCoverage,
        hasDefaultCoverage,
        selectedPatientCoverage,
        overrideCoverage,
        overridePlan,
        overrideNoCoverage,
        doctorConsultationTypes,
        doctorId,
        isDoctorCoverageExcluded,
        isCoverageAccepted,
        isConsultationTypeExcluded
    ]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        if (!formData.time) {
            setTimeslotError("Selecciona un horario para confirmar el turno.")
            return
        }
        setTimeslotError("")

        // Check if coverage is valid
        if (!isCoverageValid()) {
            // Open the coverage section to highlight the issue
            setOpenSections(prev => ({
                ...prev,
                schedule: false,
                patient: false,
                coverage: true,
                consultationType: false
            }))
            return
        }

        // Calculate the maximum duration from selectedTypes
        const maxDuration = selectedTypes.length > 0
            ? Math.max(
                ...selectedTypes.map((typeName) => {
                    const consultation = doctorConsultationTypes.find((t) => t.name === typeName);
                    return consultation ? getDuration(consultation) : effectiveConfig.appointmentSlotDuration || 15;
                })
            )
            : effectiveConfig.appointmentSlotDuration || 15;

        // Determine the coverage to use based on user selection with clear priority
        let coverageToUse = "";
        if (formData.patient) {
            if (useDefaultCoverage && hasDefaultCoverage) {
                // Use the patient's default coverage
                coverageToUse = selectedPatientCoverage;
            } else if (overrideNoCoverage) {
                // Use "Sin Cobertura" if no coverage is selected
                coverageToUse = "Sin Cobertura";
            } else if (overrideCoverage) {
                // Use the override coverage and plan
                coverageToUse = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
            } else {
                // Fallback to the patient's coverage if no override and default not used
                coverageToUse = formData.patient.coverage || "Sin Cobertura";
            }
        }

        // Update the patient object with the selected coverage
        const updatedPatient = formData.patient ? {
            ...formData.patient,
            coverage: coverageToUse
        } : null;

        onConfirm({
            ...formData,
            patient: updatedPatient,
            type: selectedTypes.join(", "),
            duration: maxDuration
        });
    };

    // Get patients who were created by or have appointments at this medical center
    const patientsWithAppointmentsAtCenter = React.useMemo(() => {
        if (!medicalCenterId) return patients

        // Use the getPatientsByMedicalCenter function from PatientContext
        return getPatientsByMedicalCenter(medicalCenterId, appointments)
    }, [patients, appointments, medicalCenterId, getPatientsByMedicalCenter])

    // Use the searchPatients function from PatientContext if there's a search term
    const filteredPatients = searchTerm.trim() ?
        searchPatients(searchTerm, medicalCenterId, appointments) :
        patientsWithAppointmentsAtCenter.filter((patient: Patient) => {
            const searchTermLower = normalizeString(searchTerm.toLowerCase()).trim()
            if (searchTermLower === "") return false
            const patientNameLower = normalizeString(patient.name.toLowerCase())
            const searchWords = searchTermLower.split(/\s+/)
            const patientWords = patientNameLower.split(/\s+/)
            const nameMatch = searchWords.every((word) =>
                patientWords.some((patientWord) => patientWord.startsWith(word))
            )
            return (
                nameMatch ||
                patient.dni.startsWith(searchTerm) ||
                patient.phone.startsWith(searchTerm) ||
                normalizeString(patient.email.toLowerCase()).startsWith(searchTermLower)
            )
        })

    const formatDate = (date: Date) => {
        const options: Intl.DateTimeFormatOptions = {weekday: "long", year: "numeric", month: "long", day: "numeric"}
        return date.toLocaleDateString("es-ES", options)
    }

    // Filter out past time slots and blocked slots
    const filteredTimeSlots = timeSlots.filter(slot => {
        // Filter out past slots
        if (isPastTimeSlot(slot)) return false;

        // Format date string for isSlotBlocked check
        const dateStr = `${selectedDate.getFullYear()}-${String(selectedDate.getMonth() + 1).padStart(2, "0")}-${String(
            selectedDate.getDate()
        ).padStart(2, "0")}`;

        // Check if the slot is blocked for this doctor and medical center
        if (isSlotBlocked(dateStr, slot, doctorId, medicalCenterId || '')) return false;

        return true;
    });

    const [patientError, setPatientError] = useState<string>("");
    const [patientNotFound, setPatientNotFound] = useState<boolean>(false);
    const [searchPerformed, setSearchPerformed] = useState<boolean>(false);

    const handleCreateNewPatient = () => {
        setPatientError(""); // Clear any previous errors

        const capitalizedName = capitalizeName(
            `${newPatientData.name.trim()} ${newPatientData.lastName.trim()}`.trim()
        )

        // Check if a patient with this DNI already exists
        const dniToCheck = newPatientData.dni.trim();

        // Validate DNI length
        if (dniToCheck.length < 7 || dniToCheck.length > 8) {
            setPatientError("El DNI debe tener entre 7 y 8 dígitos");
            return;
        }

        // Validate phone number using Google's libphonenumber
        try {
            const phoneNumber = phoneUtil.parseAndKeepRawInput(newPatientData.phone);
            const isValid = phoneUtil.isValidNumber(phoneNumber);

            if (!isValid) {
                setPhoneError("El número de teléfono no es válido");
                return;
            }
        } catch {
            setPhoneError("El número de teléfono no es válido");
            return;
        }

        // Removed check for existing patient with same DNI - allowing duplicate patients

        // Determine coverage - ensure "Sin Cobertura" has proper capitalization
        let coverage;
        if (newPatientData.noCoverage) {
            coverage = "Sin Cobertura";
        } else if (newPatientData.coverage) {
            coverage = `${newPatientData.coverage.trim()} ${newPatientData.plan.trim()}`.trim();
        } else {
            // Default to "Sin Cobertura" if nothing is selected
            coverage = "Sin Cobertura";
        }

        const newPatient: Patient = {
            // No need to set ID - PatientContext will generate one
            name: capitalizedName,
            dni: dniToCheck,
            phone: newPatientData.phone.trim(), // Phone already includes country code from PhoneInput
            email: newPatientData.email.trim(),
            coverage: coverage,
            defaultCoverage: coverage, // Set defaultCoverage to the same as coverage for new patients
            // Add fields to match patients created through registration
            emailVerified: false, // Will be verified when claimed through patient-auth-form
            phoneVerified: false, // Will be verified when claimed through patient-auth-form
            // userId and isDefault will be set when the patient is claimed
            createdByMedicalCenterId: medicalCenterId // Track which medical center created this patient
        }

        try {
            // Use addPatient from PatientContext
            const patientId = addPatient(newPatient)
            setFormData((prev) => ({
                ...prev,
                patient: {
                    id: patientId, // Use the ID returned from addPatient
                    name: newPatient.name,
                    dni: newPatient.dni, // Include the patient DNI
                    coverage: newPatient.coverage,
                    phone: newPatient.phone,
                },
            }))
            setIsCreatingNewPatient(false)
            setDropdownOpen(false)
            setSearchTerm(newPatient.name)

            // Set default coverage state
            setHasDefaultCoverage(true)
            setSelectedPatientCoverage(coverage)
            setUseDefaultCoverage(true)
            setIsCoverageAccepted(null); // Reset acceptance check on new patient
        } catch (error) {
            if (error instanceof Error) {
                setPatientError(error.message);
            } else {
                setPatientError("Error al crear el paciente. Intente nuevamente.");
            }
        }
    }

    const filteredConsultations = doctorConsultationTypes.filter((type) =>
        normalizeString(type.name.toLowerCase()).includes(normalizeString(consultationSearch.toLowerCase()))
    )


    // Helper function to show information for the selected consultation types
    const showSelectedConsultationInfo = useCallback(() => {
        // Only proceed if there are selected types and info hasn't been shown yet
        if (selectedTypes.length === 0 || infoShownForSelection) return;

        // Show info for all selected types
        showConsultationInfo(selectedTypes);

        // Mark that info has been shown for this selection
        setInfoShownForSelection(true);
    }, [
        selectedTypes,
        showConsultationInfo,
        infoShownForSelection
    ]);

    const handleConsultationSelect = (type: string) => {
        const isAlreadySelected = selectedTypes.includes(type);

        // Simply toggle the selection without showing info immediately
        setSelectedTypes((prev) =>
            isAlreadySelected ? prev.filter((t) => t !== type) : [...prev, type]
        )
        // Don't set as confirmed yet - user needs to click Confirmar or click outside
        setConsultationTypeConfirmed(false)
    }

    // Handle clicking outside of dropdowns
    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (patientSearchRef.current && !patientSearchRef.current.contains(e.target as Node)) {
                setDropdownOpen(false)
                // Reset patient not found state when dropdown is closed
                if (!formData.patient) {
                    setPatientNotFound(false)
                    setSearchPerformed(false)
                }
            }
            if (consultationRef.current && !consultationRef.current.contains(e.target as Node)) {
                setShowConsultationDropdown(false)
                setConsultationSearch("")
                // Set consultation type as confirmed when clicking outside if types are selected
                if (selectedTypes.length > 0) {
                    setConsultationTypeConfirmed(true)
                    // Show information popup after confirming selection by clicking outside
                    showSelectedConsultationInfo()
                }
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [formData.patient, selectedTypes, showSelectedConsultationInfo])

    // Function to clear patient selection
    const clearPatientSelection = () => {
        setFormData((prev) => ({
            ...prev,
            patient: null,
        }))
        setSearchTerm("")
        setHasDefaultCoverage(false)
        setSelectedPatientCoverage("")
        setUseDefaultCoverage(false)
        setOverrideCoverage("")
        setOverridePlan("")
        setOverrideNoCoverage(false)
        setIsCoverageAccepted(null); // Clear acceptance state
    }

    if (!effectiveConfig) {
        return (
            <div className="border rounded-lg p-[1rem] bg-white border-black">
                <p className="text-red-500">No se pudo cargar la configuración del doctor. Por favor, intenta de
                    nuevo.</p>
                <Button onClick={onCancel} variant="outline" className="w-full mt-[1rem]">
                    Cancelar
                </Button>
            </div>
        )
    }

    // Helper function to get summary text
    const getScheduleSummary = () => {
        if (!formData.time) return "No seleccionado"
        return formData.time
    }

    const getPatientSummary = () => {
        if (!formData.patient) return "No seleccionado"
        return formData.patient.name
    }

    const getCoverageSummary = () => {
        if (!formData.patient) return "No disponible"
        let summaryText = "";

        // Prioritize based on what's selected
        if (useDefaultCoverage && hasDefaultCoverage) {
            summaryText = selectedPatientCoverage;
        } else if (overrideNoCoverage) {
            summaryText = "Sin Cobertura";
        } else if (overrideCoverage) {
            summaryText = `${overrideCoverage} ${overridePlan}`.trim();
        }

        if (!summaryText) summaryText = "No seleccionado"; // Fallback

        // If coverage is "No seleccionado", just return it without warning
        if (summaryText === "No seleccionado") {
            return summaryText;
        }

        // Check if any selected consultation types are not accepted with this coverage
        if (selectedTypes.length > 0) {
            const isAnyTypeExcluded = selectedTypes.some(typeName => {
                const consultationType = doctorConsultationTypes.find(t => t.name === typeName);
                if (!consultationType) return false;
                return isConsultationTypeExcluded(consultationType, getCurrentCoverageId(), getCurrentPlanId());
            });

            const areAllTypesExcluded = selectedTypes.every(typeName => {
                const consultationType = doctorConsultationTypes.find(t => t.name === typeName);
                if (!consultationType) return false;
                return isConsultationTypeExcluded(consultationType, getCurrentCoverageId(), getCurrentPlanId());
            });

            if (areAllTypesExcluded) {
                // If all types are excluded, show "No aceptada"
                return (
                    <span className="flex items-center gap-1">
            <span>{summaryText} (No aceptada)</span>
          </span>
                );
            } else if (isAnyTypeExcluded && selectedTypes.length > 1) {
                // If some types are excluded but not all, show "Aceptada parcialmente"
                return (
                    <span className="flex items-center gap-1">
            <span>{summaryText} (Aceptada parcialmente)</span>
          </span>
                );
            }
        } else if (isCoverageAccepted === false) {
            // Fallback to the old behavior if no consultation types are selected
            return (
                <span className="flex items-center gap-1">
          <span>{summaryText} (No aceptada)</span>
        </span>
            );
        }

        return summaryText;
    }

    const getConsultationTypeSummary = () => {
        if (selectedTypes.length === 0) return "No seleccionado"
        return selectedTypes.join(", ")
    }

    // Determine if any section is open
    const isAnySectionOpen = Object.values(openSections).some(isOpen => isOpen);

    // Helper function to check if the current coverage is "Sin Cobertura"
    const isSinCobertura = () => {
        if (!formData.patient) return false;

        if (overrideNoCoverage) {
            return true;
        } else if (useDefaultCoverage && hasDefaultCoverage && selectedPatientCoverage === "Sin Cobertura") {
            return true;
        } else if (!useDefaultCoverage && !overrideCoverage && formData.patient.coverage === "Sin Cobertura") {
            return true;
        }

        return false;
    };

    // Helper function to check if any selected consultation type doesn't accept the current coverage
    // AND doesn't accept Sin Cobertura (private pay)
    const hasUnbookableConsultationType = () => {
        if (!formData.patient || selectedTypes.length === 0) return false;

        // Get current coverage information
        const currentCoverageId = getCurrentCoverageId();
        const currentPlanId = getCurrentPlanId();

        // Check if any selected type has both conditions:
        // 1. Doesn't accept the current coverage
        // 2. Doesn't accept Sin Cobertura (private pay)
        return selectedTypes.some(typeName => {
            const consultationType = doctorConsultationTypes.find(t => t.name === typeName);
            if (!consultationType) return false;

            // Check if current coverage is excluded
            const isCurrentCoverageExcluded = isConsultationTypeExcluded(
                consultationType,
                currentCoverageId,
                currentPlanId
            );

            // Check if Sin Cobertura is excluded (doesn't accept private pay)
            const doesNotAcceptPrivatePay = consultationType.acceptsPrivatePay === false;

            // If both conditions are true, this consultation type cannot be booked
            return isCurrentCoverageExcluded && doesNotAcceptPrivatePay;
        });
    };

    // Helper function to get the current coverage ID
    const getCurrentCoverageId = () => {
        if (!formData.patient) return "";

        let coverageToCheck = "";

        if (useDefaultCoverage && hasDefaultCoverage) {
            coverageToCheck = selectedPatientCoverage;
        } else if (overrideNoCoverage) {
            coverageToCheck = "Sin Cobertura";
        } else if (overrideCoverage) {
            coverageToCheck = overrideCoverage;
        } else if (formData.patient.coverage) {
            coverageToCheck = formData.patient.coverage;
        }

        if (coverageToCheck === "Sin Cobertura") {
            const sinCobertura = DEFAULT_COVERAGES.find(c => c.name === "Sin Cobertura");
            return sinCobertura?.id || "";
        } else {
            const foundCoverage = DEFAULT_COVERAGES.find(cov => coverageToCheck.startsWith(cov.name));
            return foundCoverage?.id || "";
        }
    };

    // Helper function to get the current plan ID
    const getCurrentPlanId = () => {
        if (!formData.patient) return null;

        let coverageToCheck = "";
        let planToCheck = "";

        if (useDefaultCoverage && hasDefaultCoverage) {
            coverageToCheck = selectedPatientCoverage;
        } else if (overrideNoCoverage) {
            return null; // Sin Cobertura has no plan
        } else if (overrideCoverage) {
            coverageToCheck = overrideCoverage;
            planToCheck = overridePlan;
        } else if (formData.patient.coverage) {
            coverageToCheck = formData.patient.coverage;
        }

        if (coverageToCheck === "Sin Cobertura") {
            return null;
        } else if (planToCheck) {
            return planToCheck;
        } else {
            // Extract plan from coverage string if present
            const foundCoverage = DEFAULT_COVERAGES.find(cov => coverageToCheck.startsWith(cov.name));
            if (foundCoverage && coverageToCheck !== foundCoverage.name) {
                const planPart = coverageToCheck.substring(foundCoverage.name.length).trim();
                if (planPart && foundCoverage.plans.includes(planPart)) {
                    return planPart;
                }
            }
            return null;
        }
    };

    // Function to check if coverage is valid (not "No seleccionado" and plan is selected when needed)
    const isCoverageValid = () => {
        if (!formData.patient) return false;

        // Get the coverage summary text based on priority
        let coverageText = "";

        if (useDefaultCoverage && hasDefaultCoverage) {
            coverageText = selectedPatientCoverage;
        } else if (overrideNoCoverage) {
            coverageText = "Sin Cobertura";
        } else if (overrideCoverage) {
            coverageText = `${overrideCoverage} ${overridePlan}`.trim();
        }

        // Check if coverage is valid (not empty and not "No seleccionado")
        if (!coverageText || coverageText === "No seleccionado") return false;

        // If a coverage is selected but plan is not selected, return false
        if (!useDefaultCoverage && overrideCoverage && !overrideNoCoverage && !overridePlan) return false;

        // Always return true if coverage is selected, even if it's not accepted
        // This allows the user to confirm the appointment with unaccepted coverage
        return true;
    };

    return (
        <>

            {isCreatingNewPatient && (
                <div className="space-y-[0.75rem] border rounded-lg p-[1rem] bg-white border-black">
                    <h3 className="font-medium text-[1.125rem]">Crear nuevo paciente</h3>
                    <div className="space-y-[0.5rem]">
                        <Label htmlFor="name">Nombre y Apellido</Label>
                        <Input
                            id="name"
                            placeholder="Nombre"
                            value={newPatientData.name}
                            autoComplete="off"
                            role="combobox"
                            aria-autocomplete="list"
                            onChange={(e) => {
                                // Only allow letters and spaces
                                const value = e.target.value.replace(/[^a-zA-ZáéíóúÁÉÍÓÚñÑüÜ\s]/g, '');
                                setNewPatientData((prev) => ({...prev, name: capitalizeName(value)}));
                            }}
                        />
                        <Input
                            id="lastName"
                            placeholder="Apellido"
                            value={newPatientData.lastName}
                            autoComplete="off"
                            role="combobox"
                            aria-autocomplete="list"
                            onChange={(e) => {
                                // Only allow letters and spaces
                                const value = e.target.value.replace(/[^a-zA-ZáéíóúÁÉÍÓÚñÑüÜ\s]/g, '');
                                setNewPatientData((prev) => ({...prev, lastName: capitalizeName(value)}));
                            }}
                        />
                    </div>
                    <div className="space-y-[0.5rem]">
                        <Label>Cobertura</Label>
                        <div className="space-y-[0.5rem]">
                            <Select
                                open={isCoverageOpen}
                                onOpenChange={setIsCoverageOpen}
                                value={newPatientData.coverage}
                                onValueChange={(value) => setNewPatientData((prev) => ({
                                    ...prev,
                                    coverage: value,
                                    plan: ""
                                }))}
                                disabled={newPatientData.noCoverage}
                            >
                                <SelectTrigger id="coverage" className="z-[51]">
                                    <SelectValue placeholder="Seleccionar Cobertura"/>
                                </SelectTrigger>
                                <SelectContent className="z-[1001]">
                                    {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map(cov => (
                                        <SelectItem key={cov.id} value={cov.name}>{cov.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Select
                                open={isPlanOpen}
                                onOpenChange={setIsPlanOpen}
                                value={newPatientData.plan}
                                onValueChange={(value) => setNewPatientData((prev) => ({...prev, plan: value}))}
                                disabled={!newPatientData.coverage || newPatientData.noCoverage}
                            >
                                <SelectTrigger id="plan" className="z-[51]">
                                    <SelectValue placeholder="Seleccionar Plan"/>
                                </SelectTrigger>
                                <SelectContent className="z-[51]">
                                    {newPatientData.coverage &&
                                        plansByCoverage[newPatientData.coverage]?.map((plan) => (
                                            <SelectItem key={plan} value={plan}>
                                                {plan}
                                            </SelectItem>
                                        ))}
                                </SelectContent>
                            </Select>
                            <div className="flex items-center space-x-[0.5rem]">
                                <Switch
                                    id="no-coverage"
                                    checked={newPatientData.noCoverage}
                                    onCheckedChange={(checked) =>
                                        setNewPatientData((prev) => ({
                                            ...prev,
                                            noCoverage: checked,
                                            coverage: checked ? "" : prev.coverage,
                                            plan: checked ? "" : prev.plan,
                                        }))
                                    }
                                />
                                <Label htmlFor="no-coverage">Sin Cobertura</Label>
                            </div>
                        </div>
                    </div>
                    <div className="space-y-[0.5rem]">
                        <Label>Información de Contacto</Label>
                        <div className="space-y-[0.5rem]">
                            <Input
                                id="dni"
                                placeholder="DNI"
                                value={newPatientData.dni}
                                autoComplete="off"
                                role="combobox"
                                aria-autocomplete="list"
                                onChange={(e) => {
                                    // Get current value and new value
                                    const currentValue = newPatientData.dni;
                                    const newValue = e.target.value;

                                    // Only allow numeric values
                                    const numericValue = newValue.replace(/[^0-9]/g, '');

                                    // Prevent DNI from starting with 0
                                    if (numericValue === "0" || (numericValue.length > 0 && numericValue[0] === "0")) {
                                        return;
                                    }

                                    // If already at max length (8) and trying to add more characters, don't update
                                    // This prevents removing the last digit when typing in the middle
                                    if (currentValue.length >= 8 && numericValue.length > 8) {
                                        return;
                                    }

                                    // Limit to 8 characters if needed
                                    const limitedValue = numericValue.length > 8 ? numericValue.slice(0, 8) : numericValue;

                                    setNewPatientData((prev) => ({...prev, dni: limitedValue}));

                                    // Clear error if DNI is valid length
                                    if (limitedValue.length >= 7 && limitedValue.length <= 8) {
                                        setPatientError("");
                                    } else if (limitedValue.length > 0) {
                                        setPatientError("El DNI debe tener entre 7 y 8 dígitos");
                                    }
                                }}
                            />
                            {patientError && (
                                <div className="text-xs text-red-600 mt-1">
                                    {patientError}
                                </div>
                            )}
                            <div className="custom-phone-input">
                                <PhoneInput
                                    defaultCountry="ar"
                                    value={newPatientData.phone}
                                    onChange={(phone) => {
                                        setNewPatientData((prev) => ({...prev, phone}));

                                        try {
                                            // Parse the phone number using Google's libphonenumber
                                            const phoneNumber = phoneUtil.parseAndKeepRawInput(phone);
                                            const isValid = phoneUtil.isValidNumber(phoneNumber);

                                            if (!isValid) {
                                                setPhoneError("El número de teléfono no es válido");
                                            } else {
                                                setPhoneError("");
                                            }
                                        } catch {
                                            setPhoneError("El número de teléfono no es válido");
                                        }
                                    }}
                                    inputStyle={{
                                        width: '100%',
                                        height: '2.5rem'
                                    }}
                                    className="w-full custom-phone-input with-dial-code-preview"
                                    placeholder="Teléfono"
                                    countrySelectorStyleProps={{
                                        buttonStyle: {
                                            paddingLeft: '10px',
                                            paddingRight: '5px'
                                        }
                                    }}
                                    hideDropdown={false}
                                    disableDialCodeAndPrefix={true}
                                    showDisabledDialCodeAndPrefix={true}
                                    disableFormatting={false}
                                    preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                                    countries={getSpanishCountries()}
                                />
                                {phoneError && (
                                    <div className="text-xs text-red-600 mt-1">
                                        {phoneError}
                                    </div>
                                )}
                            </div>
                            <Input
                                id="email"
                                placeholder="Email (opcional)"
                                value={newPatientData.email}
                                autoComplete="off"
                                role="combobox"
                                aria-autocomplete="list"
                                onChange={(e) => setNewPatientData((prev) => ({...prev, email: e.target.value}))}
                            />
                        </div>
                    </div>

                    <div className="flex gap-[0.75rem] mt-[1.5rem]">
                        <Button onClick={() => setIsCreatingNewPatient(false)} variant="outline" className="flex-1">
                            Cancelar
                        </Button>
                        <Button
                            onClick={handleCreateNewPatient}
                            className="flex-1 bg-blue-500 hover:bg-blue-600"
                            disabled={!newPatientData.name || !newPatientData.lastName || !newPatientData.dni || !newPatientData.phone || newPatientData.phone === "+54" || !!phoneError}
                        >
                            Crear
                        </Button>
                    </div>
                </div>
            )}

            {!isCreatingNewPatient && (
                <form onSubmit={handleSubmit}
                      className="flex flex-col gap-[1.5rem] border rounded-lg p-[1rem] bg-white border-black">
                    <div>
                        <h2 className="text-[1.125rem] font-semibold mb-[1rem]">
                            Agendar nuevo turno {effectiveConfig.name ? `para ${effectiveConfig.name}` : ""}
                        </h2>
                        <div className="text-[0.875rem] text-gray-600">{formatDate(selectedDate)}</div>
                    </div>

                    <div className="space-y-2">
                        <CollapsibleSection
                            title="Horario"
                            isOpen={openSections.schedule}
                            onOpenChange={() => toggleSection("schedule")}
                            summary={getScheduleSummary()}
                            isAnySectionOpen={isAnySectionOpen}
                            isComplete={!!formData.time}
                        >
                            <div className="space-y-[0.5rem]">
                                <Select
                                    value={formData.time}
                                    onValueChange={(value: string) => {
                                        setFormData((prev) => ({...prev, time: value}))
                                        if (value) {
                                            setTimeslotError("")

                                            // Determine which section to open next based on what's already filled
                                            if (formData.patient) {
                                                // Open consultation type section since patient is already selected
                                                setOpenSections(prev => ({
                                                    ...prev,
                                                    schedule: false,
                                                    patient: false,
                                                    coverage: false,
                                                    consultationType: true
                                                }))
                                            } else {
                                                // No patient selected yet, open patient section
                                                setOpenSections(prev => ({
                                                    ...prev,
                                                    schedule: false,
                                                    patient: true,
                                                    coverage: false,
                                                    consultationType: false
                                                }))
                                            }
                                        }
                                    }}
                                    disabled={!!selectedTime}
                                >
                                    <SelectTrigger id="time" className="flex justify-between">
                                        <div className="text-left"><SelectValue placeholder="Seleccionar horario"/>
                                        </div>
                                    </SelectTrigger>
                                    <SelectContent className="z-[51]">
                                        {filteredTimeSlots.map((slot: string) => (
                                            <SelectItem key={slot} value={slot}>
                                                {slot} {!availableSlots.includes(slot) && "(Sobreturno)"}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {timeslotError && <p className="text-red-500 text-[0.875rem]">{timeslotError}</p>}
                            </div>
                        </CollapsibleSection>

                        <CollapsibleSection
                            title="Paciente"
                            isOpen={openSections.patient}
                            onOpenChange={() => toggleSection("patient")}
                            summary={getPatientSummary()}
                            isAnySectionOpen={isAnySectionOpen}
                            isComplete={!!formData.patient}
                        >
                            <div className="space-y-[0.5rem]" ref={patientSearchRef}>
                                <div className="relative">
                                    <Input
                                        id="patient"
                                        placeholder="Buscar paciente"
                                        value={searchTerm}
                                        autoComplete="off"
                                        name="search-patient"
                                        role="combobox"
                                        aria-autocomplete="list"
                                        onFocus={() => !formData.patient && setDropdownOpen(true)}
                                        onChange={(e) => {
                                            if (!formData.patient) {
                                                setSearchTerm(e.target.value)
                                                setDropdownOpen(true)
                                                setPatientNotFound(false)
                                                setSearchPerformed(false)
                                            }
                                        }}
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter' && !formData.patient) {
                                                e.preventDefault()
                                                setSearchPerformed(true)
                                                const trimmedSearch = searchTerm.trim()
                                                if (trimmedSearch === '') {
                                                    setPatientNotFound(false)
                                                    return
                                                }

                                                const exactDniMatch = patients.find(p => p.dni === trimmedSearch)
                                                if (exactDniMatch) {
                                                    setPatientNotFound(false)
                                                    setFormData((prev) => ({
                                                        ...prev,
                                                        patient: {
                                                            id: exactDniMatch.id || '', // Include the patient ID
                                                            name: exactDniMatch.name,
                                                            dni: exactDniMatch.dni, // Include the patient DNI
                                                            coverage: exactDniMatch.coverage,
                                                            phone: exactDniMatch.phone,
                                                        },
                                                    }))
                                                    setSearchTerm(exactDniMatch.name)
                                                    setDropdownOpen(false)

                                                    // Handle default coverage
                                                    if (exactDniMatch.coverage) {
                                                        setHasDefaultCoverage(true)
                                                        setSelectedPatientCoverage(exactDniMatch.coverage)
                                                        setUseDefaultCoverage(true)
                                                        setOverrideNoCoverage(false)
                                                    } else {
                                                        setHasDefaultCoverage(true)
                                                        setSelectedPatientCoverage("Sin Cobertura")
                                                        setUseDefaultCoverage(true)
                                                        setOverrideCoverage('')
                                                        setOverridePlan('')
                                                        setOverrideNoCoverage(false)
                                                    }
                                                } else {
                                                    // Check if there are any matching patients in the filtered list
                                                    const hasMatches = filteredPatients.length > 0
                                                    setPatientNotFound(!hasMatches)
                                                }
                                            }
                                        }}
                                        ref={patientInputRef}
                                        className={formData.patient ? 'bg-gray-100 text-gray-700' : ''}
                                        readOnly={!!formData.patient}
                                    />
                                    {formData.patient ? (
                                        <div
                                            className="absolute right-[0.75rem] top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                                            <Info
                                                className="opacity-50 w-[1rem] h-[1rem] cursor-pointer hover:opacity-100 text-blue-500 hover:text-blue-700"
                                                onClick={() => {
                                                    if (formData.patient) {
                                                        // Find the full patient object from the patients array
                                                        const fullPatient = patients.find(p => p.id === formData.patient?.id);
                                                        if (fullPatient) {
                                                            setSelectedPatientForDetails(fullPatient);
                                                            setShowPatientDetails(true);
                                                        }
                                                    }
                                                }}
                                            />
                                            <X
                                                className="opacity-50 w-[1rem] h-[1rem] cursor-pointer hover:opacity-100 text-gray-500 hover:text-red-500"
                                                onClick={clearPatientSelection}
                                            />
                                        </div>
                                    ) : (
                                        <Search
                                            className="absolute right-[0.75rem] top-1/2 transform -translate-y-1/2 opacity-50 w-[1rem] h-[1rem] cursor-pointer hover:opacity-100"
                                            onClick={() => {
                                                if (!formData.patient) {
                                                    patientInputRef.current?.focus()
                                                    setDropdownOpen(true)

                                                    // Perform search when clicking the search icon
                                                    const trimmedSearch = searchTerm.trim()
                                                    if (trimmedSearch !== '') {
                                                        setSearchPerformed(true)
                                                        const exactDniMatch = patients.find(p => p.dni === trimmedSearch)
                                                        if (exactDniMatch) {
                                                            setPatientNotFound(false)
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                patient: {
                                                                    id: exactDniMatch.id || '',
                                                                    name: exactDniMatch.name,
                                                                    dni: exactDniMatch.dni,
                                                                    coverage: exactDniMatch.coverage,
                                                                    phone: exactDniMatch.phone,
                                                                },
                                                            }))
                                                            setSearchTerm(exactDniMatch.name)
                                                            setDropdownOpen(false)

                                                            // Handle default coverage
                                                            if (exactDniMatch.coverage) {
                                                                setHasDefaultCoverage(true)
                                                                setSelectedPatientCoverage(exactDniMatch.coverage)
                                                                setUseDefaultCoverage(true)
                                                                setOverrideNoCoverage(false)
                                                            } else {
                                                                setHasDefaultCoverage(true)
                                                                setSelectedPatientCoverage("Sin Cobertura")
                                                                setUseDefaultCoverage(true)
                                                                setOverrideCoverage('')
                                                                setOverridePlan('')
                                                                setOverrideNoCoverage(false)
                                                            }
                                                        } else {
                                                            // Check if there are any matching patients in the filtered list
                                                            const hasMatches = filteredPatients.length > 0
                                                            setPatientNotFound(!hasMatches)
                                                        }
                                                    }
                                                }
                                            }}
                                        />
                                    )}
                                    {dropdownOpen && (
                                        <div
                                            className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-[0.25rem] shadow-lg">
                                            <div className="max-h-[15rem] overflow-auto">
                                                {patientNotFound && searchPerformed ? (
                                                    <div className="p-[0.5rem] text-[0.75rem] text-red-500">
                                                        No se encontró ningún paciente con los datos ingresados.
                                                    </div>
                                                ) : (
                                                    filteredPatients.map((patient: Patient) => (
                                                        <div
                                                            key={patient.id}
                                                            className={`${dropdownItemStyle} ${formData.patient?.id === patient.id ? 'bg-blue-50' : ''} flex justify-between items-start`}
                                                        >
                                                            <div
                                                                className="flex-grow cursor-pointer"
                                                                onClick={() => {
                                                                    setFormData((prev) => ({
                                                                        ...prev,
                                                                        patient: {
                                                                            id: patient.id || '', // Include the patient ID
                                                                            name: patient.name,
                                                                            dni: patient.dni, // Include the patient DNI
                                                                            coverage: patient.coverage || "Sin Cobertura", // Default to Sin Cobertura if null/undefined
                                                                            phone: patient.phone,
                                                                        },
                                                                    }))
                                                                    setSearchTerm(patient.name)
                                                                    setDropdownOpen(false)

                                                                    // Handle default coverage
                                                                    const patientCov = patient.coverage || "Sin Cobertura";
                                                                    setHasDefaultCoverage(true)
                                                                    setSelectedPatientCoverage(patientCov)
                                                                    setUseDefaultCoverage(true)
                                                                    setOverrideCoverage('')
                                                                    setOverridePlan('')
                                                                    setOverrideNoCoverage(false)
                                                                }}
                                                            >
                                                                <div className="font-medium">{patient.name}</div>
                                                                <div
                                                                    className="text-[0.75rem] text-gray-500">DNI: {patient.dni}</div>
                                                            </div>
                                                            <div
                                                                className="ml-2 p-1 cursor-pointer text-blue-500 hover:text-blue-700"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    setSelectedPatientForDetails(patient);
                                                                    setShowPatientDetails(true);
                                                                }}
                                                            >
                                                                <Info size={16}/>
                                                            </div>
                                                        </div>
                                                    ))
                                                )}
                                            </div>
                                            <div
                                                className={`${dropdownButtonStyle} cursor-pointer`}
                                                onClick={() => setIsCreatingNewPatient(true)}
                                            >
                                                Crear nuevo paciente
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </CollapsibleSection>

                        {formData.patient && hasDefaultCoverage && (
                            <CollapsibleSection
                                title="Cobertura"
                                isOpen={openSections.coverage}
                                onOpenChange={() => toggleSection("coverage")}
                                summary={getCoverageSummary()}
                                isAnySectionOpen={isAnySectionOpen}
                                hasWarning={isCoverageAccepted === false || (selectedTypes.length > 0 && selectedTypes.some(typeName => {
                                    const consultationType = doctorConsultationTypes.find(t => t.name === typeName);
                                    return consultationType && isConsultationTypeExcluded(consultationType, getCurrentCoverageId(), getCurrentPlanId());
                                }))}
                                isComplete={isCoverageValid()}
                            >
                                <div className="space-y-[0.5rem]">
                                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-2">
                                        <div className="flex flex-col space-y-3">
                                            <div className="flex flex-col">
                                                <p className="text-sm text-blue-800">Cobertura registrada:</p>
                                                <p className="text-sm font-medium text-blue-700 mt-1">{selectedPatientCoverage}</p>
                                            </div>
                                            <div
                                                className="flex items-center justify-between pt-2 border-t border-blue-200">
                                                <Label htmlFor="use-default-coverage"
                                                       className="text-sm font-medium text-blue-800">
                                                    {useDefaultCoverage ? "Usar esta cobertura" : "Usar otra cobertura"}
                                                </Label>
                                                <Switch
                                                    id="use-default-coverage"
                                                    checked={useDefaultCoverage}
                                                    onCheckedChange={(checked) => {
                                                        setUseDefaultCoverage(checked);
                                                        if (checked) {
                                                            // Reset override values when switching back to default coverage
                                                            setOverrideCoverage("");
                                                            setOverridePlan("");
                                                            setOverrideNoCoverage(false);
                                                        }
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    {!useDefaultCoverage && (
                                        <div className="space-y-[0.5rem] border-t border-gray-200 pt-3">
                                            <div className="space-y-[0.5rem]">
                                                <Select
                                                    value={overrideCoverage}
                                                    onValueChange={(value) => {
                                                        setOverrideCoverage(value);
                                                        setOverridePlan("");
                                                    }}
                                                    disabled={overrideNoCoverage}
                                                >
                                                    <SelectTrigger id="override-coverage"
                                                                   className="flex justify-between z-[51]">
                                                        <div className="text-left"><SelectValue
                                                            placeholder="Seleccionar Cobertura"/></div>
                                                    </SelectTrigger>
                                                    <SelectContent className="z-[1001]">
                                                        {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map(cov => (
                                                            <SelectItem key={cov.id}
                                                                        value={cov.name}>{cov.name}</SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <Select
                                                    value={overridePlan}
                                                    onValueChange={(value) => setOverridePlan(value)}
                                                    disabled={!overrideCoverage || overrideNoCoverage}
                                                >
                                                    <SelectTrigger id="override-plan"
                                                                   className="flex justify-between z-[51]">
                                                        <div className="text-left"><SelectValue
                                                            placeholder="Seleccionar Plan"/></div>
                                                    </SelectTrigger>
                                                    <SelectContent className="z-[51]">
                                                        {overrideCoverage &&
                                                            plansByCoverage[overrideCoverage]?.map((plan) => (
                                                                <SelectItem key={plan} value={plan}>
                                                                    {plan}
                                                                </SelectItem>
                                                            ))}
                                                    </SelectContent>
                                                </Select>
                                                {selectedPatientCoverage !== "Sin Cobertura" && (
                                                    <div className="flex items-center space-x-[0.5rem]">
                                                        <Switch
                                                            id="override-no-coverage"
                                                            checked={overrideNoCoverage}
                                                            onCheckedChange={(checked) => {
                                                                setOverrideNoCoverage(checked);
                                                                if (checked) {
                                                                    setOverrideCoverage("");
                                                                    setOverridePlan("");
                                                                }
                                                            }
                                                            }
                                                        />
                                                        <Label htmlFor="override-no-coverage">Sin Cobertura</Label>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CollapsibleSection>
                        )}

                        <CollapsibleSection
                            title="Tipo de atención"
                            isOpen={openSections.consultationType}
                            onOpenChange={() => toggleSection("consultationType")}
                            summary={getConsultationTypeSummary()}
                            isAnySectionOpen={isAnySectionOpen}
                            isComplete={selectedTypes.length > 0 && consultationTypeConfirmed}
                        >
                            <div className="space-y-[0.5rem]" ref={consultationRef}>
                                <div className="relative">
                                    <div className="relative">
                                        <Input
                                            id="type"
                                            placeholder="Buscar tipo de atención"
                                            value={consultationSearch}
                                            autoComplete="off"
                                            name="search-consultation"
                                            role="combobox"
                                            aria-autocomplete="list"
                                            onFocus={() => setShowConsultationDropdown(true)}
                                            onChange={(e) => {
                                                setConsultationSearch(e.target.value)
                                                setShowConsultationDropdown(true)
                                            }}
                                            ref={consultationInputRef}
                                        />
                                        <Search
                                            className="absolute right-[0.75rem] top-1/2 transform -translate-y-1/2 opacity-50 w-[1rem] h-[1rem] cursor-pointer"
                                            onClick={() => {
                                                consultationInputRef.current?.focus()
                                                setShowConsultationDropdown(true)
                                            }}/>
                                    </div>

                                    {showConsultationDropdown && (
                                        <div
                                            className="absolute z-20 w-full bg-white border border-gray-200 rounded-md mt-[0.25rem] shadow-lg">
                                            <div className="max-h-[15rem] overflow-auto">
                                                {doctorConsultationTypes.length === 0 ? (
                                                    <div className="p-2 text-sm text-gray-500">No hay tipos de atención
                                                        configurados</div>
                                                ) : (
                                                    filteredConsultations.map((type) => (
                                                        <div
                                                            key={type.name}
                                                            className={cn(
                                                                dropdownItemStyle,
                                                                selectedTypes.includes(type.name)
                                                                    ? 'text-white bg-blue-500 hover:bg-blue-400'
                                                                    : 'hover:bg-gray-100'
                                                            )}
                                                            onClick={() => handleConsultationSelect(type.name)}
                                                        >
                                                            {type.name}
                                                            {getDuration(type) > (effectiveConfig.appointmentSlotDuration || 15) &&
                                                                ` (${getDuration(type)} min)`}
                                                            {/* Show price when coverage is "Sin Cobertura" or when coverage is not accepted */}
                                                            {(isSinCobertura() || isConsultationTypeExcluded(type, getCurrentCoverageId(), getCurrentPlanId())) &&
                                                                type.acceptsPrivatePay !== false &&
                                                                ` - $${type.basePrice.toLocaleString('es-AR', {minimumFractionDigits: 0}).replace(/,/g, '.')}`}
                                                        </div>
                                                    ))
                                                )}
                                            </div>
                                            <div className="border-t p-2 flex justify-between items-center">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="text-gray-600 hover:bg-gray-100 h-9"
                                                    onClick={() => {
                                                        setShowAllConsultationsModal(true)
                                                        setShowConsultationDropdown(false)
                                                    }}
                                                >
                                                    Ver todas
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    className="bg-blue-500 hover:bg-blue-600 text-white h-9"
                                                    onClick={() => {
                                                        // Mark consultation type as confirmed when clicking Confirmar button
                                                        if (selectedTypes.length > 0) {
                                                            setConsultationTypeConfirmed(true)
                                                            setShowConsultationDropdown(false)
                                                            // Show information popup after confirming selection
                                                            showSelectedConsultationInfo()
                                                        } else {
                                                            setShowConsultationDropdown(false)
                                                        }
                                                    }}
                                                >
                                                    Confirmar
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                <div className="flex flex-wrap gap-[0.5rem] mt-[0.5rem]">
                                    {selectedTypes.map((type) => (
                                        <span key={type}
                                              className="px-[0.5rem] py-[0.25rem] bg-blue-100 text-blue-800 rounded-full text-[0.75rem] hover:bg-blue-200 flex items-center">
                      {type}
                                            {(() => {
                                                // Check if the consultation type has info to show
                                                const consultationType = doctorConsultationTypes.find(t => t.name === type);

                                                // Check for basic info (instructions, medical order)
                                                const hasBasicInfo = consultationType &&
                                                    (consultationType.requiresMedicalOrder ||
                                                        (consultationType.hasInstructions && consultationType.instructions));

                                                // Check for coverage-related info (copay, exclusions)
                                                let hasCoverageInfo = false;

                                                if (consultationType && formData.patient) {
                                                    // Get current coverage information
                                                    let coverageToCheck = "";

                                                    if (useDefaultCoverage && hasDefaultCoverage) {
                                                        coverageToCheck = selectedPatientCoverage;
                                                    } else if (overrideNoCoverage) {
                                                        coverageToCheck = "Sin Cobertura";
                                                    } else if (overrideCoverage) {
                                                        coverageToCheck = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
                                                    } else if (formData.patient.coverage) {
                                                        coverageToCheck = formData.patient.coverage;
                                                    }

                                                    if (coverageToCheck) {
                                                        // Check for Sin Cobertura
                                                        if (coverageToCheck === "Sin Cobertura") {
                                                            // Show icon if consultation doesn't accept private pay
                                                            hasCoverageInfo = consultationType.acceptsPrivatePay === false;
                                                        } else {
                                                            // For other coverages, check for copays and exclusions
                                                            const foundCoverage = DEFAULT_COVERAGES.find(cov => coverageToCheck.startsWith(cov.name));
                                                            if (foundCoverage) {
                                                                const coverageId = foundCoverage.id;

                                                                // Extract plan if present
                                                                let planId: string | null = null;
                                                                if (coverageToCheck !== foundCoverage.name) {
                                                                    const planPart = coverageToCheck.substring(foundCoverage.name.length).trim();
                                                                    if (planPart && foundCoverage.plans.includes(planPart)) {
                                                                        planId = planPart;
                                                                    }
                                                                }

                                                                // Check for copays
                                                                const hasCopay = consultationType.copays?.some(
                                                                    c => c.coverageId === coverageId &&
                                                                        (c.planId === null || c.planId === planId)
                                                                );

                                                                // Check for exclusions
                                                                const isExcluded = consultationType.excludedCoverages?.some(
                                                                    exclusion =>
                                                                        exclusion.coverageId === coverageId &&
                                                                        (exclusion.planId === null || exclusion.planId === planId)
                                                                );

                                                                hasCoverageInfo = !!hasCopay || !!isExcluded;
                                                            }
                                                        }
                                                    }
                                                }

                                                // Only render the button if there's any info to show
                                                return (hasBasicInfo || hasCoverageInfo) ? (
                                                    <button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            e.preventDefault();
                                                            // This button still shows info immediately when clicked
                                                            showConsultationInfo([type]);
                                                        }}
                                                        className="ml-[0.25rem] rounded-full hover:bg-blue-300 px-[0.25rem]"
                                                        aria-label={`Ver información de ${type}`}
                                                        type="button" // Explicitly set type to button to prevent form submission
                                                    >
                                                        <AlertCircle className="h-3 w-3 text-blue-600"/>
                                                    </button>
                                                ) : null;
                                            })()}
                                            <button
                                                onClick={() => {
                                                    setSelectedTypes((prev) => prev.filter((t) => t !== type))
                                                    // If removing a type, check if there are still types selected
                                                    // If no types left, set confirmed to false
                                                    setConsultationTypeConfirmed(prev => prev && selectedTypes.length > 1)
                                                }}
                                                className="ml-[0.25rem] rounded-full hover:bg-blue-200 px-[0.375rem]"
                                                aria-label={`Remover ${type}`}
                                            >
                        ×
                      </button>
                    </span>
                                    ))}
                                </div>
                            </div>
                        </CollapsibleSection>
                    </div>

                    {/* Warning message for unbookable consultation types */}
                    {hasUnbookableConsultationType() && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg mb-3">
                            <p className="text-red-700 flex items-start gap-2">
                                <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5"/>
                                <span className="text-sm text-justify">
                  No se puede reservar este turno porque uno o más tipos de atención seleccionados no aceptan la cobertura actual ni pagos particulares.
                </span>
                            </p>
                        </div>
                    )}

                    <div className="flex gap-[0.75rem] mt-[0.2rem]">
                        <Button onClick={onCancel} variant="outline" className="flex-1">
                            Cancelar
                        </Button>
                        <Button
                            type="submit"
                            className="flex-1 bg-blue-500 hover:bg-blue-600 text-white"
                            disabled={
                                !formData.patient ||
                                !formData.time ||
                                selectedTypes.length === 0 ||
                                !isCoverageValid() ||
                                !consultationTypeConfirmed ||
                                // Disable if any selected type doesn't accept Sin Cobertura
                                (isSinCobertura() && selectedTypes.some(typeName => {
                                    const consultationType = doctorConsultationTypes.find(t => t.name === typeName);
                                    return consultationType && consultationType.acceptsPrivatePay === false;
                                })) ||
                                // Disable if any selected type doesn't accept current coverage AND doesn't accept Sin Cobertura
                                hasUnbookableConsultationType()
                            }
                        >
                            Confirmar
                        </Button>
                    </div>

                    {showAllConsultationsModal && (
                        <div className="fixed inset-0 bg-black/50 z-[50] flex items-center justify-center"
                             onClick={() => setShowAllConsultationsModal(false)}>
                            <div
                                className="w-full max-w-[37.5rem] bg-white rounded-xl shadow-2xl overflow-hidden max-h-[80vh] mx-[1rem]"
                                onClick={(e) => e.stopPropagation()}
                            >
                                <div className="p-[1.5rem] border-b bg-gray-50">
                                    <h3 className="text-[1.25rem] font-semibold text-gray-800">Seleccionar tipos de
                                        atención</h3>
                                </div>

                                <div className="max-h-[60vh] overflow-auto p-[1rem] grid gap-[0.25rem]">
                                    {doctorConsultationTypes.map((type) => (
                                        <div
                                            key={type.name}
                                            className={`p-[0.75rem] text-[0.875rem] rounded-lg cursor-pointer transition-colors flex items-center justify-between ${
                                                selectedTypes.includes(type.name) ? 'bg-blue-500 text-white hover:bg-blue-400' : 'hover:bg-gray-100'
                                            }`}
                                        >
                                            <div onClick={() => handleConsultationSelect(type.name)}>
                                                {type.name}
                                                {getDuration(type) > (effectiveConfig.appointmentSlotDuration || 15) &&
                                                    ` (${getDuration(type)} min)`}
                                                {/* Show price in the "Ver todas" modal when coverage is "Sin Cobertura" or not accepted */}
                                                {(isSinCobertura() || isConsultationTypeExcluded(type, getCurrentCoverageId(), getCurrentPlanId())) &&
                                                    type.acceptsPrivatePay !== false &&
                                                    ` - $${type.basePrice.toLocaleString('es-AR', {minimumFractionDigits: 0}).replace(/,/g, '.')}`}
                                            </div>
                                            {(() => {
                                                // Check for basic info (instructions, medical order)
                                                const hasBasicInfo = type.requiresMedicalOrder ||
                                                    (type.hasInstructions && type.instructions);

                                                // Check for coverage-related info (copay, exclusions)
                                                let hasCoverageInfo = false;

                                                if (formData.patient) {
                                                    // Get current coverage information
                                                    let coverageToCheck = "";

                                                    if (useDefaultCoverage && hasDefaultCoverage) {
                                                        coverageToCheck = selectedPatientCoverage;
                                                    } else if (overrideNoCoverage) {
                                                        coverageToCheck = "Sin Cobertura";
                                                    } else if (overrideCoverage) {
                                                        coverageToCheck = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
                                                    } else if (formData.patient.coverage) {
                                                        coverageToCheck = formData.patient.coverage;
                                                    }

                                                    if (coverageToCheck) {
                                                        // Check for Sin Cobertura
                                                        if (coverageToCheck === "Sin Cobertura") {
                                                            // Show icon if consultation doesn't accept private pay
                                                            hasCoverageInfo = type.acceptsPrivatePay === false;
                                                        } else {
                                                            // For other coverages, check for copays and exclusions
                                                            const foundCoverage = DEFAULT_COVERAGES.find(cov => coverageToCheck.startsWith(cov.name));
                                                            if (foundCoverage) {
                                                                const coverageId = foundCoverage.id;

                                                                // Extract plan if present
                                                                let planId: string | null = null;
                                                                if (coverageToCheck !== foundCoverage.name) {
                                                                    const planPart = coverageToCheck.substring(foundCoverage.name.length).trim();
                                                                    if (planPart && foundCoverage.plans.includes(planPart)) {
                                                                        planId = planPart;
                                                                    }
                                                                }

                                                                // Check for copays
                                                                const hasCopay = type.copays?.some(
                                                                    c => c.coverageId === coverageId &&
                                                                        (c.planId === null || c.planId === planId)
                                                                );

                                                                // Check for exclusions
                                                                const isExcluded = type.excludedCoverages?.some(
                                                                    exclusion =>
                                                                        exclusion.coverageId === coverageId &&
                                                                        (exclusion.planId === null || exclusion.planId === planId)
                                                                );

                                                                hasCoverageInfo = !!hasCopay || !!isExcluded;
                                                            }
                                                        }
                                                    }
                                                }

                                                // Only render the button if there's any info to show
                                                return (hasBasicInfo || hasCoverageInfo) ? (
                                                    <button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            e.preventDefault();
                                                            // This button still shows info immediately when clicked
                                                            showConsultationInfo([type.name]);
                                                        }}
                                                        className={`ml-2 rounded-full p-1 ${
                                                            selectedTypes.includes(type.name) ? 'hover:bg-blue-600 text-white' : 'hover:bg-gray-200 text-blue-600'
                                                        }`}
                                                        aria-label={`Ver información de ${type.name}`}
                                                        type="button" // Explicitly set type to button to prevent form submission
                                                    >
                                                        <AlertCircle className="h-4 w-4"/>
                                                    </button>
                                                ) : null;
                                            })()}
                                        </div>
                                    ))}
                                </div>

                                <div className="flex gap-[1rem] p-[1rem] border-t bg-gray-50">
                                    <Button
                                        variant="outline"
                                        className="flex-1"
                                        onClick={() => setShowAllConsultationsModal(false)}
                                    >
                                        Cancelar
                                    </Button>
                                    <Button
                                        className="flex-1 bg-blue-500 hover:bg-blue-600 text-white"
                                        onClick={() => {
                                            // Mark consultation type as confirmed when confirming selection in modal
                                            if (selectedTypes.length > 0) {
                                                setConsultationTypeConfirmed(true)
                                                setShowAllConsultationsModal(false)
                                                // Show information popup after confirming selection
                                                showSelectedConsultationInfo()
                                            } else {
                                                setShowAllConsultationsModal(false)
                                            }
                                        }}
                                    >
                                        Confirmar selección
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}
                </form>
            )}

            {/* Patient Details Dialog */}
            <PatientDetailsDialog
                patient={selectedPatientForDetails}
                isOpen={showPatientDetails}
                onClose={() => setShowPatientDetails(false)}
                appointments={appointments}
            />

            {/* Instructions Dialog */}
            <ConsultationInfoTable
                isOpen={showInstructionsDialog}
                onOpenChange={setShowInstructionsDialog}
                consultationTypesInfo={consultationTypesInfo}
                selectedCoverage={
                    useDefaultCoverage && hasDefaultCoverage
                        ? selectedPatientCoverage
                        : overrideNoCoverage
                            ? "Sin Cobertura"
                            : overrideCoverage
                                ? `${overrideCoverage} ${overridePlan}`.trim()
                                : formData.patient?.coverage || "No seleccionada"
                }
            />
        </>
    )
}

