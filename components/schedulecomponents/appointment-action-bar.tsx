import {useEffect, useRef, useState} from "react"
import {AppointmentState} from "@/types/professional-schedules"
// Patient type is handled by PatientContext
import {<PERSON><PERSON>} from "@/components/ui/button"
import {CancelAppointmentDialog} from "@/components/schedulecomponents/cancel-appointment-dialog"
import {AppointmentContextMenu} from "@/components/ui/appointment-context-menu"
import {AppointmentDetailsDialog} from "@/components/schedulecomponents/appointment-details-dialog"
import {AppointmentStatusModal} from "@/components/schedulecomponents/appointment-status-modal"
import {AppointmentModifyModal} from "@/components/schedulecomponents/appointment-modify-modal"
import {cancelAppointment} from "@/app/api/utils/appointment/SlotApiUtils"
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {AppointmentConsultationType} from "@/types/consultationTypes/ConsultationType";


interface AppointmentActionBarProps {
    onObservationsClick: () => void
    currentStatus: AppointmentState
    onStatusChange: (newStatus: AppointmentState) => void
    onModifyAppointment: (date: Date, time: string, consultationTypes: AppointmentConsultationType[], doctorIdParam: number, duration?: number) => void;
    onCancelAppointment: () => void
    appointment: ProfessionalAppointment
    availableDates: Date[]
    availableTimeSlots: string[]
    contextMenu?: { x: number; y: number } | null
    setContextMenu?: (value: { x: number; y: number } | null) => void
    onAction: (action: string) => void
    view?: "day" | "week" | "month"
    appointments: Record<string, ProfessionalAppointment[]>
    doctors: DoctorsForMedicalCenter[]
    doctorInfo: DoctorsForMedicalCenter | undefined
    employeeUserId: number,
    onFloatingComponentChange?: (isOpen: boolean) => void,
    refetchSchedulesForMonth: (date: Date) => Promise<void>
}

export function AppointmentActionBar({
                                         onObservationsClick,
                                         currentStatus,
                                         onStatusChange,
                                         onModifyAppointment,
                                         appointment,
                                         contextMenu,
                                         setContextMenu,
                                         view,
                                         appointments,
                                         doctors,
                                         doctorInfo,
                                         employeeUserId,
                                         onFloatingComponentChange,
                                         refetchSchedulesForMonth
                                     }: AppointmentActionBarProps) {


    const [floatingComponent, setFloatingComponent] = useState<string | null>(null)
    const barRef = useRef<HTMLDivElement>(null)
    const consultationRef = useRef<HTMLDivElement>(null)
    const [showInstructionsDialog, setShowInstructionsDialog] = useState(false)

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (barRef.current && !barRef.current.contains(e.target as Node) && contextMenu) {
                setContextMenu?.(null)
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [contextMenu, setContextMenu])

    useEffect(() => {
        onFloatingComponentChange?.(!!floatingComponent)
    }, [floatingComponent, onFloatingComponentChange])

    if (!appointment) return null

    const handleContextAction = (action: string) => {
        setContextMenu?.(null)
        setFloatingComponent(action)
    }

    return (doctorInfo &&
        <>
            {view === "day" && (
                <div
                    ref={barRef}
                    className="fixed bottom-12 left-1/2 -translate-x-1/2 bg-blue-50 rounded-lg border shadow-lg z-50 py-2 px-4 w-[calc(100%-24rem)] max-w-3xl"
                >
                    <div className="flex items-center justify-center gap-4">
                        <Button variant="outline" onClick={() => setFloatingComponent("status")}>
                            Estado de turno
                        </Button>
                        <Button variant="outline" onClick={onObservationsClick}>
                            Detalles de turno
                        </Button>
                        <Button variant="outline" onClick={() => setFloatingComponent("modify")}>
                            Modificar turno
                        </Button>
                        <Button
                            variant="outline"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => setFloatingComponent("cancel")}
                        >
                            Cancelar turno
                        </Button>
                    </div>
                </div>
            )}

            {contextMenu && view === "week" && (
                <AppointmentContextMenu
                    x={contextMenu.x}
                    y={contextMenu.y}
                    onAction={handleContextAction}
                    onClose={() => setContextMenu?.(null)}
                />
            )}

            {floatingComponent === "status" && (
                <AppointmentStatusModal
                    isOpen={true}
                    onClose={() => setFloatingComponent(null)}
                    currentStatus={currentStatus}
                    onStatusChange={onStatusChange}
                />
            )}

            {floatingComponent === "observations" && (
                <div className="fixed inset-0 bg-black/50 z-50">
                    <AppointmentDetailsDialog
                        appointment={appointment}
                        isOpen={true}
                        onClose={() => setFloatingComponent(null)}
                        // Patient management now handled by PatientContext
                        doctorInfo={doctorInfo}
                    />
                </div>
            )}

            {floatingComponent === "modify" && (
                <AppointmentModifyModal
                    isOpen={true}
                    onClose={() => setFloatingComponent(null)}
                    appointment={appointment}
                    doctors={doctors}
                    doctorId={doctorInfo.id}
                    appointments={appointments}
                    onModifyAppointment={onModifyAppointment}
                    refetchSchedulesForMonth={refetchSchedulesForMonth}
                />
            )}

            {floatingComponent === "cancel" && (
                <CancelAppointmentDialog
                    isOpen={true}
                    onClose={() => setFloatingComponent(null)}
                    appointment={appointment}
                    onConfirm={(option, reason) => {
                        // TODO : use option and reason on cancel appointment
                        cancelAppointment(appointment.id, employeeUserId)
                        setFloatingComponent(null)
                    }}
                />
            )}
        </>
    )
}
