"use client"

import { useEffect, useState } from "react"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { usePathname, useRouter, useSearchParams } from "next/navigation"

interface Props {
  noInsurance: boolean
  setNoInsurance: (value: boolean) => void
  onApplyFilters?: (coverageData: { noInsurance: boolean; coverageId: string; coverageName: string; plan: string }) => void
  onTimeFilterChange?: (value: string) => void
  onSortChange?: (value: "date" | "distance") => void
  sortBy?: "date" | "distance"
  searchType?: string
  searchQuery?: string
  locationQuery?: string
}

export default function SearchFiltersDesktop({ onTimeFilterChange, onSortChange, sortBy = "date" }: Props) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Controlled time based on URL
  const timeValue = searchParams.get("time") || "all"

  const updateParam = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set(name, value)
    router.replace(`${pathname}?${params.toString()}`)
    if (name === "time" && onTimeFilterChange) onTimeFilterChange(value)
    if (name === "sort" && onSortChange) onSortChange(value as "date" | "distance")
  }

  return (
    <div className="p-4 md:p-5">
      <div className="hidden md:grid grid-cols-2 gap-6">
        {/* Col 1: Orden */}
        <div className="space-y-5">
          <div>
            <Label htmlFor="sort" className="text-sm font-medium text-gray-700 mb-1 block">
              Ordenar por
            </Label>
            <Select value={sortBy} onValueChange={(v: "date" | "distance") => updateParam("sort", v)}>
              <SelectTrigger id="sort" className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
                <SelectValue placeholder="Seleccionar orden" />
              </SelectTrigger>
              <SelectContent className="z-[90]">
                <SelectItem value="date">Primer turno disponible</SelectItem>
                <SelectItem value="distance">Menor distancia (requiere ubicación)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Col 2: Horario */}
        <div className="border-l md:pl-6">
          <Label htmlFor="time" className="text-sm font-medium text-gray-700 mb-1 block">Horario</Label>
          <Select value={timeValue} onValueChange={(v) => updateParam("time", v)}>
            <SelectTrigger id="time" className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
              <SelectValue placeholder="Seleccionar horario" />
            </SelectTrigger>
            <SelectContent className="z-[90]">
              <SelectItem value="all">Todos</SelectItem>
              <SelectItem value="morning">Mañana (hasta 12:00)</SelectItem>
              <SelectItem value="afternoon">Tarde (12:00 a 17:00)</SelectItem>
              <SelectItem value="evening">Noche (desde 17:00)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}


