import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>evron<PERSON>eft, ChevronRight, MapPin, User, Building2} from "lucide-react"
import {But<PERSON>} from "@/components/ui/button"
import Link from "next/link"
import React, {useCallback, useContext, useEffect, useRef, useState} from "react"

// ExpandableText component for truncating long text
interface ExpandableTextProps {
    text: string;
    maxLength: number;
    className?: string;
}

const ExpandableText: React.FC<ExpandableTextProps> = ({ text, maxLength, className = "" }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    
    if (text.length <= maxLength) {
        return <p className={className}>{text}</p>;
    }
    
    const truncatedText = text.substring(0, maxLength);
    const remainingLength = text.length - maxLength;
    
    // Only show expand/collapse if there are more than 4 characters remaining
    if (remainingLength <= 4) {
        return <p className={className}>{text}</p>;
    }
    
    const displayText = isExpanded ? text : truncatedText + "...";
    const toggleText = isExpanded ? "menos" : "más";
    
    return (
        <p className={className}>
            {displayText}
            <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-blue-600 hover:text-blue-800 font-medium ml-1 cursor-pointer"
            >
                {toggleText}
            </button>
        </p>
    );
};
import {Doctor} from "@/types/doctor"
import {MedicalCenter} from "@/types/medical-center"
import {doctors as staticDoctors} from "@/data/doctors"
import {initialMedicalCenters as staticMedicalCenters} from "@/data/medicalCenters"
import {getAvailableTimeSlots} from "@/utils/appointmentUtils"
import {addDays, format, startOfDay} from "date-fns"
import {useAppointments} from "@/contexts/AppointmentContext"
import {es} from "date-fns/locale"
import {DEFAULT_COVERAGES} from "@/data/coverages"
import {CoverageContext} from "@/contexts/CoverageContext"
import {getCABALocationsFromMedicalCenters, getGBALocationsFromMedicalCenters, getLocationById, getLocationByName} from "@/data/locations"

// Local helper from cita components used here as well
const LocationFormatType = {
    MEDICAL_CENTER_SIMPLE: 'MEDICAL_CENTER_SIMPLE'
} as const

function formatMedicalCenterLocation(medicalCenter: MedicalCenter, _format: typeof LocationFormatType[keyof typeof LocationFormatType]): string {
    // Prefer address if present; fallback to name
    if (medicalCenter.address) return medicalCenter.address
    return medicalCenter.name
}

export interface Result {
    id: string
    doctorId?: string // Original doctor ID when id is a composite
    name: string
    image: string
    establishmentName: string // Medical center name
    address: string // Street and region
    distanceInKm?: number // Actual distance in kilometers
    price?: string
    copay?: string
    priceLabel?: string // Custom label for price/copay display
    appointments: string[]
    healthInsurance?: {
        name: string
        plan: string
    }
    type: "doctor" | "medicalCenter"
    specialties?: string[]
    medicalCenterId?: string
    nextAvailableDate?: Date
    nextAvailableTime?: string
    latitude?: number // Medical center latitude
    longitude?: number // Medical center longitude
}

interface SearchResultsListProps {
    noInsurance: boolean;
    searchQuery?: string;
    searchType?: string;
    locationQuery?: string;
    coverage?: string;
    plan?: string;
    timeOfDay?: string;
    sortBy?: "date" | "distance"; // Add sort parameter
    onResultsChange?: (results: Result[]) => void;
    // UX sync with map
    onItemHover?: (id: string) => void;
    onItemLeave?: () => void;
    highlightedResultId?: string;
}

export default function SearchResultsList({
                                              noInsurance,
                                              searchQuery = "",
                                              searchType = "",
                                              locationQuery = "",
                                              coverage = "",
                                              plan = "",
                                              timeOfDay = "all",
                                              sortBy = "date", // Default to date sorting
                                              onResultsChange,
                                              onItemHover,
                                              onItemLeave,
                                              highlightedResultId
                                          }: SearchResultsListProps) {
    const [results, setResults] = useState<Result[]>([])
    const [filteredResults, setFilteredResults] = useState<Result[]>([])
    const [loading, setLoading] = useState<boolean>(true)
  const [mapReady, setMapReady] = useState<boolean>(false)
  // Build a filter key to sync with map readiness notifications
  const filterKey = `${searchQuery}-${searchType}-${noInsurance}-${coverage}-${plan}-${timeOfDay}`
  const currentFilterKeyRef = useRef<string>(filterKey)
  useEffect(() => { currentFilterKeyRef.current = filterKey }, [filterKey])

  // Listen for map readiness for the current filter key
  useEffect(() => {
      const handler = (e: any) => {
          try {
              const k = e?.detail?.key
              if (k && k === currentFilterKeyRef.current) {
                  setMapReady(true)
              }
          } catch {}
      }
      window.addEventListener('map-ready', handler as any)
      return () => { window.removeEventListener('map-ready', handler as any) }
  }, [])

    const [currentPage, setCurrentPage] = useState<number>(1)
    const resultsPerPage = 10
    const [totalPages, setTotalPages] = useState(1)
    const {appointments, blockedSlots} = useAppointments()
    const {isDoctorCoverageExcluded} = useContext(CoverageContext)

    // Add state for user location
    const [userLocation, setUserLocation] = useState<{ latitude: number, longitude: number } | null>(null)
    const [locationLoading, setLocationLoading] = useState<boolean>(false)
    const [locationError, setLocationError] = useState<string | null>(null)

    // Create a ref to hold the previous filtered results for comparison
    const prevFilteredResultsRef = useRef<string>("");

    // Get user location for distance calculation (only when sorting by distance)
    useEffect(() => {
        // Only request location if we're sorting by distance and don't have it yet
        if (sortBy === "distance" && !userLocation && !locationLoading && !locationError) {
            // Check if geolocation is available in the browser
            if (navigator.geolocation) {
                setLocationLoading(true);
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        setUserLocation({
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude
                        });
                        setLocationLoading(false);
                        setLocationError(null);
                    },
                    (error) => {
                        console.warn("Error getting user location:", error);
                        setLocationLoading(false);
                        // Provide more user-friendly error messages
                        let errorMessage = "No se pudo obtener su ubicación";
                        if (error.code === 1) {
                            errorMessage = "Permiso de ubicación denegado";
                        } else if (error.code === 2) {
                            errorMessage = "Ubicación no disponible";
                        } else if (error.code === 3) {
                            errorMessage = "Tiempo de espera agotado";
                        }
                        setLocationError(errorMessage);
                        // If we can't get the user's location, we'll continue without distance calculation
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 300000 // 5 minutes cache
                    }
                );
            } else {
                setLocationError("Geolocation not supported");
            }
        }
    }, [sortBy, userLocation, locationLoading, locationError]);

    // Determine coverage selection state more precisely
    // A complete selection means either:
    // - Sin Cobertura is selected (noInsurance is true)
    // - Both a coverage AND a plan are selected
    const isFullySelected = noInsurance || (coverage !== "" && plan !== "");

    // Selection is in progress when a coverage is selected but no plan yet
    // This includes the case when a user had a complete selection and changed the coverage
    const isSelectionInProgress = !noInsurance && coverage !== "" && plan === "";

    // Helper function to check if a time is within a specified range - wrap in useCallback
    const isTimeInRange = useCallback((hour: number, range: string): boolean => {
        switch (range) {
            case 'morning':
                return hour < 12; // Before noon
            case 'afternoon':
                return hour >= 12 && hour < 17; // 12 PM to 5 PM
            case 'evening':
                return hour >= 17; // 5 PM onwards
            default:
                return true; // 'all' or invalid range
        }
    }, []);

    // Helper function to calculate distance between two coordinates using Haversine formula
    const calculateDistance = useCallback((lat1: number, lng1: number, lat2: number, lng2: number): number => {
        // Earth's radius in kilometers
        const R = 6371;

        // Convert degrees to radians
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;

        // Haversine formula
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng / 2) * Math.sin(dLng / 2);

        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c; // Distance in kilometers

        return distance;
    }, []);

    // Listen for requestResults events
    const containerRef = useRef<HTMLDivElement>(null);
  const pendingScrollIdRef = useRef<string | null>(null);

    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        const handleRequestResults = () => {
            if (onResultsChange && filteredResults.length > 0) {
                onResultsChange(filteredResults);
            }
        };

        container.addEventListener('requestResults', handleRequestResults);

        return () => {
            container.removeEventListener('requestResults', handleRequestResults);
        };
    }, [filteredResults, onResultsChange]);

  // Listen for navigate-to-result events from the map to jump to the right page and scroll to the item
  useEffect(() => {
      const handler = (e: any) => {
          try {
              const id = e?.detail?.id as string | undefined;
              if (!id) return;
              const idx = filteredResults.findIndex(r => r.id === id);
              if (idx === -1) return;
              const pageNumber = Math.floor(idx / resultsPerPage) + 1;
              pendingScrollIdRef.current = id;
              if (currentPage !== pageNumber) {
                  setCurrentPage(pageNumber);
              } else {
                  // Scroll immediately if already on the correct page
                  setTimeout(() => {
                      try {
                          const candidates = Array.from(document.querySelectorAll(`[id="result-${id}"]`)) as HTMLElement[];
                          const target = candidates.find((el) => {
                              const visible = el.offsetParent !== null || el.getClientRects().length > 0;
                              const list = el.closest('[data-results-list="true"]') as HTMLElement | null;
                              const listVisible = !!list && (list.offsetParent !== null || list.getClientRects().length > 0);
                              return visible && listVisible;
                          }) || candidates[0];
                          target?.scrollIntoView({ behavior: 'smooth', block: 'center' });
                          pendingScrollIdRef.current = null;
                      } catch {}
                  }, 0);
              }
          } catch {}
      };
      window.addEventListener('navigate-to-result', handler as any);
      return () => { window.removeEventListener('navigate-to-result', handler as any); };
  }, [filteredResults, resultsPerPage, currentPage]);

  // After pagination updates, complete any pending scroll
  useEffect(() => {
      const id = pendingScrollIdRef.current;
      if (!id) return;
      // Defer to ensure DOM for new page is rendered
      const raf = requestAnimationFrame(() => {
          try {
              const candidates = Array.from(document.querySelectorAll(`[id="result-${id}"]`)) as HTMLElement[];
              const target = candidates.find((el) => {
                  const visible = el.offsetParent !== null || el.getClientRects().length > 0;
                  const list = el.closest('[data-results-list="true"]') as HTMLElement | null;
                  const listVisible = !!list && (list.offsetParent !== null || list.getClientRects().length > 0);
                  return visible && listVisible;
              }) || candidates[0];
              if (target) {
                  target.scrollIntoView({ behavior: 'smooth', block: 'center' });
                  pendingScrollIdRef.current = null;
              }
          } catch {}
      });
      return () => { try { cancelAnimationFrame(raf); } catch {} };
  }, [currentPage, filteredResults]);


    useEffect(() => {
        setLoading(true)
        setMapReady(false)
        const searchResults: Result[] = []

        // Function to normalize strings for search (remove accents)
        const normalizeString = (str: string) => {
            return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "")
        }

        // Find the Sin Cobertura coverage ID for exclusion checks
        const sinCoberturaCoverage = DEFAULT_COVERAGES.find(c => c.name === "Sin Cobertura")
        const sinCoberturaId = sinCoberturaCoverage?.id || ""

        // Define functions inside useEffect to avoid dependency issues
        // Get next available appointments
        const getNextAvailableAppointments = (doctor: Doctor, medicalCenters: MedicalCenter[]) => {
            const today = startOfDay(new Date())
            const appointmentsForDoctor: string[] = []
            const maxDaysToCheck = 7 // Limit to 7 days for performance
            let firstAvailableDate: Date | null = null
            let firstAvailableTime: string | null = null
            let medicalCenterId: string | null = null

            // Find all medical centers this doctor works at
            const doctorCenters = medicalCenters.filter(center =>
                center.doctors.includes(doctor.id)
            )

            if (doctorCenters.length === 0) return {
                appointments: [],
                firstAvailableDate: null,
                firstAvailableTime: null,
                medicalCenterId: null
            }

            // Use the first medical center for simplicity (we could enhance this later)
            const medicalCenter = doctorCenters[0]
            medicalCenterId = medicalCenter.id

            // Check the next days for available slots
            for (let i = 0; i < maxDaysToCheck; i++) {
                const checkDate = addDays(today, i)

                // Get available slots for this date
                const slots = getAvailableTimeSlots(
                    doctor,
                    medicalCenter,
                    checkDate,
                    Object.values(appointments).flat(),
                    blockedSlots,
                    doctor.id,
                    medicalCenter.id
                )

                // Filter slots by time of day if specified
                const filteredSlots = timeOfDay !== 'all'
                    ? slots.filter(slot => isTimeInRange(parseInt(slot.split(':')[0], 10), timeOfDay))
                    : slots;

                // If we found slots, format them and add to results
                if (filteredSlots.length > 0) {
                    // Set the first available appointment info if not already set
                    if (firstAvailableDate === null) {
                        firstAvailableDate = checkDate
                        firstAvailableTime = filteredSlots[0]
                    }

                    // Take up to 4 slots
                    const formattedDaySlots = filteredSlots.slice(0, 4).map(time => {
                        const day = format(checkDate, 'EEE', {locale: es}).substring(0, 3)
                        const dayNumber = format(checkDate, 'dd')
                        return `${day} ${dayNumber} - ${time}`
                    })

                    appointmentsForDoctor.push(...formattedDaySlots)

                    // If we have 4 appointments, we can stop
                    if (appointmentsForDoctor.length >= 4) break
                }
            }

            return {
                appointments: appointmentsForDoctor,
                firstAvailableDate,
                firstAvailableTime,
                medicalCenterId
            }
        }

        // Check if doctor's location matches search location
        const doctorMatchesLocation = (doctor: Doctor, medicalCenters: MedicalCenter[], searchLocation: string): boolean => {
            // No filter case
            if (!searchLocation || searchLocation === "all" || searchLocation === "Todas las ubicaciones") return true

            // Get all medical centers where this doctor works
            const doctorCenters = medicalCenters.filter(center =>
                center.doctors.includes(doctor.id)
            )

            // Check if the search location is a region (caba or gba)
            if (searchLocation === "caba" || searchLocation === "Capital Federal") {
                // Check if any of the doctor's medical centers are in CABA
                return doctorCenters.some(center => center.locationRegion === "caba")
            } else if (searchLocation === "gba" || searchLocation === "Gran Buenos Aires") {
                // Check if any of the doctor's medical centers are in GBA
                return doctorCenters.some(center => center.locationRegion === "gba")
            }

            // Handle area-based search (from "Buscar en esta zona" feature)
            if (searchLocation.startsWith("Área ")) {
                try {
                    // Extract coordinates from the format "Área lat,lng"
                    const coordsStr = searchLocation.replace("Área ", "");
                    const [latStr, lngStr] = coordsStr.split(",");
                    const searchLat = parseFloat(latStr);
                    const searchLng = parseFloat(lngStr);

                    // Skip if coordinates are invalid
                    if (isNaN(searchLat) || isNaN(searchLng)) {
                        console.warn("Invalid coordinates in area search:", searchLocation);
                        return false;
                    }

                    // Check if any of the doctor's medical centers are within a reasonable distance
                    // of the search coordinates (approximately 5km)
                    const MAX_DISTANCE_KM = 5;

                    return doctorCenters.some(center => {
                        if (center.location?.latitude && center.location?.longitude) {
                            // Calculate distance using the Haversine formula
                            const lat1 = center.location.latitude;
                            const lng1 = center.location.longitude;
                            const lat2 = searchLat;
                            const lng2 = searchLng;

                            // Earth's radius in kilometers
                            const R = 6371;

                            // Convert degrees to radians
                            const dLat = (lat2 - lat1) * Math.PI / 180;
                            const dLng = (lng2 - lng1) * Math.PI / 180;

                            // Haversine formula
                            const a =
                                Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                                Math.sin(dLng / 2) * Math.sin(dLng / 2);

                            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
                            const distance = R * c; // Distance in kilometers

                            return distance <= MAX_DISTANCE_KM;
                        }
                        return false;
                    });
                } catch (error) {
                    console.error("Error processing area search:", error);
                    return false;
                }
            }

            // Check if the search location is a specific barrio or municipio by name
            // First try to find a location by ID
            const locationById = getLocationById(searchLocation)

            if (locationById) {
                // Check if any of the doctor's medical centers match this specific location ID
                return doctorCenters.some(center => center.locationId === locationById.id)
            }

            // If not found by ID, try to find by name among ALL known locations
            const byName = getLocationByName(searchLocation)
            if (byName) {
                return doctorCenters.some(center => center.locationId === byName.id)
            }

            // Fallback: check if any center's address contains the search location
            const normalizedLocation = normalizeString(searchLocation.toLowerCase())
            return doctorCenters.some(center => {
                const centerAddress = center.address || center.name
                return normalizeString(centerAddress.toLowerCase()).includes(normalizedLocation)
            })
        }

        // Normalize the search query for comparison
        const normalizedQuery = normalizeString(searchQuery.toLowerCase())

        // Helper function to get the lowest price from Primera Consulta or Consulta General
        const getLowestPrimaryConsultationPrice = (consultationTypes: any[]) => {
            if (!consultationTypes || consultationTypes.length === 0) return null;
            
            const primaryTypes = consultationTypes.filter(ct => 
                ct.name.toLowerCase().includes('primera consulta') ||
                ct.name.toLowerCase().includes('consulta general')
            );
            
            if (primaryTypes.length === 0) return null;
            
            const lowest = primaryTypes.reduce((lowest, current) => 
                current.basePrice < lowest.basePrice ? current : lowest
            );
            
            return {
                price: lowest.basePrice,
                name: lowest.name
            };
        };

        // Helper function to get copay information for specialty search
        const getSpecialtyCopayInfo = (consultationTypes: any[], coverage: string, plan?: string) => {
            if (!consultationTypes || consultationTypes.length === 0) return null;
            
            const foundCoverage = DEFAULT_COVERAGES.find((c: any) => c.name === coverage);
            if (!foundCoverage) return null;
            
            const coverageId = foundCoverage.id;
            const planId = plan || null;
            
            // First check Primera Consulta or Consulta General for copay
            const primaryTypes = consultationTypes.filter(ct => 
                ct.name.toLowerCase().includes('primera consulta') ||
                ct.name.toLowerCase().includes('consulta general')
            );
            
            for (const type of primaryTypes) {
                const copay = type.copays?.find((c: any) => 
                    c.coverageId === coverageId && 
                    (c.planId === null || c.planId === planId)
                );
                if (copay) {
                    return { type: 'specific', amount: copay.amount, label: type.name };
                }
            }
            
            // Check all types for any copay
            const hasAnyCopay = consultationTypes.some(ct => 
                ct.copays?.some((c: any) => 
                    c.coverageId === coverageId && 
                    (c.planId === null || c.planId === planId)
                )
            );
            
            return hasAnyCopay ? { type: 'general' } : null;
        };

        // Note: "profesional" search type is handled separately and not in this component

        if (searchType?.toLowerCase() === "especialidad") {
            // Search for doctors with matching specialty
            const allDoctors = staticDoctors
            const allMedicalCenters = staticMedicalCenters

            const matchingDoctors = allDoctors.filter(doctor => {
                // Check if doctor has the queried specialty
                const hasSpecialty = doctor.specialties?.some(specialty =>
                    normalizeString(specialty.toLowerCase()).includes(normalizedQuery)
                )

                // Check location if provided
                const matchesLocation = !locationQuery || doctorMatchesLocation(doctor, allMedicalCenters, locationQuery)

                // If "Sin Cobertura" is selected, check if doctor accepts private patients
                let acceptsCoverage = true;
                if (noInsurance && sinCoberturaId) {
                    // Check if Sin Cobertura is excluded for this doctor
                    acceptsCoverage = !isDoctorCoverageExcluded(doctor.id, sinCoberturaId);

                    // Additionally check if the doctor has at least one consultation type that accepts private pay
                    if (acceptsCoverage) {
                        acceptsCoverage = doctor.consultationTypes?.some(ct =>
                            ct.acceptsPrivatePay !== false && ct.availableOnline !== false
                        ) ?? true;
                    }
                } else if (!noInsurance && coverage) {
                    // For other coverages, check if the doctor accepts this coverage
                    const foundCoverage = DEFAULT_COVERAGES.find(c => c.name === coverage);

                    if (foundCoverage) {
                        // If a specific plan is selected, check if the doctor accepts that plan
                        if (plan) {
                            acceptsCoverage = !isDoctorCoverageExcluded(doctor.id, foundCoverage.id, plan);
                        } else {
                            // Otherwise, check if the doctor accepts the coverage generally
                            acceptsCoverage = !isDoctorCoverageExcluded(doctor.id, foundCoverage.id);
                        }
                    }
                }

                return hasSpecialty && matchesLocation && acceptsCoverage
            })

            // Convert doctors to result format - one result per doctor per medical center
            matchingDoctors.forEach(doctor => {
                // Find all medical centers this doctor works at
                const doctorCenters = allMedicalCenters.filter(center =>
                    center.doctors.includes(doctor.id)
                )

                if (doctorCenters.length === 0) return;

                // Create a separate result for each medical center
                doctorCenters.forEach(medicalCenter => {
                    // Get next available appointment times for this specific doctor-center combination
                    const slots = getNextAvailableAppointments(doctor, [medicalCenter])

                    const {
                        appointments: nextAppointments,
                        firstAvailableDate,
                        firstAvailableTime,
                        medicalCenterId
                    } = slots

                    // Only add entries that have available appointments matching the time filter
                    if (nextAppointments.length > 0) {
                        // Calculate distance if we have user location and medical center coordinates
                        let distanceInKm: number | undefined = undefined;
                        let distanceText = "";

                        // Always calculate distance when userLocation is available (regardless of sort method)
                        if (userLocation && medicalCenter?.location?.latitude && medicalCenter?.location?.longitude) {
                            distanceInKm = calculateDistance(
                                userLocation.latitude,
                                userLocation.longitude,
                                medicalCenter.location.latitude,
                                medicalCenter.location.longitude
                            );

                            // Format distance text
                            if (distanceInKm < 1) {
                                distanceText = `${Math.round(distanceInKm * 1000)} m`;
                            } else {
                                distanceText = `${distanceInKm.toFixed(1)} km`;
                            }
                        }

                        // Format the location for this specific medical center
                        const locationText = formatMedicalCenterLocation(
                            medicalCenter,
                            LocationFormatType.MEDICAL_CENTER_SIMPLE
                        )

                        // Create a unique ID for this doctor-medical center combination
                        const uniqueResultId = `${doctor.id}-${medicalCenterId}`;

                        // Get pricing information based on new logic
                        let price: string | undefined = undefined;
                        let copay: string | undefined = undefined;
                        let priceLabel: string | undefined = undefined;

                        if (noInsurance) {
                            // For private pay, get lowest price from Primera Consulta or Consulta General
                            const primaryPrice = getLowestPrimaryConsultationPrice(doctor.consultationTypes || []);
                            if (primaryPrice) {
                                price = `$${primaryPrice.price}`;
                                priceLabel = primaryPrice.name;
                            }
                        } else if (coverage) {
                            // For insurance, get copay information
                            const copayInfo = getSpecialtyCopayInfo(doctor.consultationTypes || [], coverage, plan);
                            if (copayInfo) {
                                if (copayInfo.type === 'specific') {
                                    copay = `$${copayInfo.amount}`;
                                    priceLabel = `${copayInfo.label} con copago`;
                                } else if (copayInfo.type === 'general') {
                                    copay = undefined;
                                    priceLabel = "Algunos estudios con copago";
                                }
                            }
                        }

                        searchResults.push({
                            id: uniqueResultId, // Use the combined ID for uniqueness
                            doctorId: doctor.id, // Keep the original doctor ID for reference
                            name: `Dr. ${doctor.name}`,
                            image: "/images/doctor-icon.png",
                            establishmentName: medicalCenter.name,
                            address: locationText,
                            distanceInKm: distanceInKm,
                            price: price,
                            copay: copay,
                            priceLabel: priceLabel, // Add custom label for specialty search
                            appointments: nextAppointments,
                            type: "doctor",
                            specialties: doctor.specialties,
                            medicalCenterId: medicalCenterId || "",
                            nextAvailableDate: firstAvailableDate || undefined,
                            nextAvailableTime: firstAvailableTime || undefined
                        })
                    }
                })
            })
        } else if (searchType?.toLowerCase() === "estudio") {
            // Search for doctors with matching consultation type
            const allDoctors = staticDoctors
            const allMedicalCenters = staticMedicalCenters

            const matchingDoctors = allDoctors.filter(doctor => {
                // Check if doctor has the queried consultation type and if it's available online
                const hasConsultationType = doctor.consultationTypes?.some(type =>
                    normalizeString(type.name.toLowerCase()).includes(normalizedQuery) &&
                    type.availableOnline !== false
                )

                // Check location if provided
                const matchesLocation = !locationQuery || doctorMatchesLocation(doctor, allMedicalCenters, locationQuery)

                // If checking with insurance, verify that this doctor accepts the coverage for this consultation type
                let acceptsCoverage = true;
                if (!noInsurance && coverage) {
                    // First, check if the doctor accepts this coverage (and plan if specified)
                    const foundCoverage = DEFAULT_COVERAGES.find(c => c.name === coverage);

                    if (foundCoverage) {
                        // If a specific plan is selected, check if the doctor accepts that plan
                        if (plan) {
                            acceptsCoverage = !isDoctorCoverageExcluded(doctor.id, foundCoverage.id, plan);
                        } else {
                            // Otherwise, check if the doctor accepts the coverage generally
                            acceptsCoverage = !isDoctorCoverageExcluded(doctor.id, foundCoverage.id);
                        }
                    }

                    // Only if the doctor accepts the coverage generally, check consultation type specifics
                    if (acceptsCoverage) {
                        // Find the matching consultation type
                        const matchingType = doctor.consultationTypes?.find(type =>
                            normalizeString(type.name.toLowerCase()).includes(normalizedQuery) &&
                            type.availableOnline !== false
                        );

                        if (matchingType) {
                            // Find if this coverage is excluded for this consultation type
                            const foundCoverage = DEFAULT_COVERAGES.find((c: {
                                name: string;
                                id: string
                            }) => c.name === coverage);

                            if (foundCoverage) {
                                const coverageId = foundCoverage.id;
                                let planId = null;

                                // Extract plan ID if provided
                                if (plan) {
                                    planId = plan;
                                }

                                // Check if this consultation type excludes this coverage+plan for this doctor
                                const isExcluded = matchingType.excludedCoverages?.some(
                                    exclusion => exclusion.coverageId === coverageId &&
                                        (exclusion.planId === null || exclusion.planId === planId)
                                );

                                acceptsCoverage = !isExcluded;
                            }
                        } else {
                            // If we can't find the consultation type, assume it's not compatible
                            acceptsCoverage = false;
                        }
                    }
                } else if (noInsurance && sinCoberturaId) {
                    // First check if the doctor accepts Sin Cobertura coverage at all
                    acceptsCoverage = !isDoctorCoverageExcluded(doctor.id, sinCoberturaId);

                    // Then check if the specific consultation type accepts private pay
                    if (acceptsCoverage) {
                        const matchingType = doctor.consultationTypes?.find(type =>
                            normalizeString(type.name.toLowerCase()).includes(normalizedQuery) &&
                            type.availableOnline !== false
                        );
                        acceptsCoverage = matchingType?.acceptsPrivatePay !== false;
                    }
                }

                return hasConsultationType && matchesLocation && acceptsCoverage
            })

            // Convert doctors to result format - one result per doctor per medical center
            matchingDoctors.forEach(doctor => {
                // Find the matching consultation type
                const matchingType = doctor.consultationTypes?.find(type =>
                    normalizeString(type.name.toLowerCase()).includes(normalizedQuery)
                )

                // Find all medical centers this doctor works at
                const doctorCenters = allMedicalCenters.filter(center =>
                    center.doctors.includes(doctor.id)
                )

                if (doctorCenters.length === 0) return;

                // Create a separate result for each medical center
                doctorCenters.forEach(medicalCenter => {
                    // Get next available appointment times for this specific doctor-center combination
                    const slots = getNextAvailableAppointments(doctor, [medicalCenter])

                    const {
                        appointments: nextAppointments,
                        firstAvailableDate,
                        firstAvailableTime,
                        medicalCenterId
                    } = slots

                    // Only add entries that have available appointments matching the time filter
                    if (nextAppointments.length > 0) {
                        // If we have a specific coverage, check for copay with both coverage and plan
                        let copayAmount = undefined;
                        if (!noInsurance && coverage && matchingType) {
                            const foundCoverage = DEFAULT_COVERAGES.find((c: {
                                name: string;
                                id: string
                            }) => c.name === coverage);

                            if (foundCoverage) {
                                const coverageId = foundCoverage.id;
                                let planId = null;

                                // Extract plan ID if provided
                                if (plan) {
                                    planId = plan;
                                }

                                // Find the copay for this coverage+plan combination
                                const copay = matchingType.copays?.find(
                                    c => c.coverageId === coverageId &&
                                        (c.planId === null || c.planId === planId)
                                );

                                if (copay) {
                                    copayAmount = `$${copay.amount}`;
                                }
                            }
                        }

                        // Calculate distance if we have user location and medical center coordinates
                        let distanceInKm: number | undefined = undefined;
                        let distanceText = "";

                        // Always calculate distance when userLocation is available (regardless of sort method)
                        if (userLocation && medicalCenter?.location?.latitude && medicalCenter?.location?.longitude) {
                            distanceInKm = calculateDistance(
                                userLocation.latitude,
                                userLocation.longitude,
                                medicalCenter.location.latitude,
                                medicalCenter.location.longitude
                            );

                            // Format distance text
                            if (distanceInKm < 1) {
                                distanceText = `${Math.round(distanceInKm * 1000)} m`;
                            } else {
                                distanceText = `${distanceInKm.toFixed(1)} km`;
                            }
                        }

                        // Format the location for this specific medical center
                        const locationText = formatMedicalCenterLocation(
                            medicalCenter,
                            LocationFormatType.MEDICAL_CENTER_SIMPLE
                        )

                        // Create a unique ID for this doctor-medical center combination
                        const uniqueResultId = `${doctor.id}-${medicalCenterId}`;

                        searchResults.push({
                            id: uniqueResultId, // Use the combined ID for uniqueness
                            doctorId: doctor.id, // Keep the original doctor ID for reference
                            name: `Dr. ${doctor.name}`,
                            image: "/images/doctor-icon.png",
                            establishmentName: medicalCenter.name,
                            address: locationText,
                            distanceInKm: distanceInKm,
                            price: noInsurance && matchingType ? `$${matchingType.basePrice}` : undefined,
                            copay: !noInsurance ? copayAmount : undefined,
                            appointments: nextAppointments,
                            type: "doctor",
                            specialties: doctor.specialties,
                            medicalCenterId: medicalCenterId || "",
                            nextAvailableDate: firstAvailableDate || undefined,
                            nextAvailableTime: firstAvailableTime || undefined,
                        })
                    }
                })
            })
        }

		// TEMP: Mock results for design preview when there are no real results
		// Includes acceptance rules for private pay and OSDE 210 for testing filters
		// TODO FACU.Remove this block when wiring to real backend search
		if (searchResults.length === 0) {
			const today = startOfDay(new Date())
			const d1 = addDays(today, 1)
			const d2 = addDays(today, 2)

			const day1 = format(d1, 'EEE', {locale: es}).substring(0, 3)
			const day2 = format(d2, 'EEE', {locale: es}).substring(0, 3)

			// Define mock candidates with acceptance metadata
			const candidates: Array<{
				meta: { acceptsPrivatePay: boolean; accepts: Array<{ name: string; plans: string[] }> }
				result: Result
			}> = [
				{
					meta: { acceptsPrivatePay: true, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-1-mc-1',
						doctorId: 'dr-fake-1',
						name: 'Dr. Mariana Pérez',
						image: '/images/doctor-icon.png',
						establishmentName: 'Centro Médico Verdi',
						address: 'Av. del Libertador 5860, CABA',
						distanceInKm: undefined,
						price: '$6.500',
						copay: '$1.500',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 10:30`,
							`${day1} ${format(d1, 'dd')} - 14:15`,
							`${day2} ${format(d2, 'dd')} - 09:00`,
							`${day2} ${format(d2, 'dd')} - 16:45`
						],
						type: 'doctor',
						                        specialties: ['Clínica Médica', 'Cardiología'],
						medicalCenterId: 'medical-center-1',
						nextAvailableDate: d1,
						nextAvailableTime: '10:30',
						latitude: -34.558125,
						longitude: -58.4466987
					}
				},
				{
					meta: { acceptsPrivatePay: false, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-2-mc-1',
						doctorId: 'dr-fake-2',
						name: 'Dr. Tomás Gutiérrez',
						image: '/images/doctor-icon.png',
						establishmentName: 'Centro Médico Verdi',
						address: 'Av. del Libertador 5860, CABA',
						distanceInKm: undefined,
						price: '$7.200',
						copay: '$2.000',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 11:00`,
							`${day1} ${format(d1, 'dd')} - 12:30`,
							`${day2} ${format(d2, 'dd')} - 15:00`,
							`${day2} ${format(d2, 'dd')} - 18:15`
						],
						type: 'doctor',
						                        specialties: ['Dermatología'],
						medicalCenterId: 'medical-center-1',
						nextAvailableDate: d1,
						nextAvailableTime: '11:00',
						latitude: -34.558125,
						longitude: -58.4466987
					}
				},
				{
					meta: { acceptsPrivatePay: true, accepts: [] },
					result: {
						id: 'dr-fake-3-mc-2',
						doctorId: 'dr-fake-3',
						name: 'Dra. Sofía Morales',
						image: '/images/doctor-icon.png',
						establishmentName: 'Clínica San Martín',
						address: 'Av. Rivadavia 4521, CABA',
						distanceInKm: undefined,
						price: '$5.800',
						copay: '$1.200',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 09:45`,
							`${day1} ${format(d1, 'dd')} - 13:10`,
							`${day2} ${format(d2, 'dd')} - 10:20`,
							`${day2} ${format(d2, 'dd')} - 15:40`
						],
						type: 'doctor',
						                        specialties: ['Pediatría'],
						medicalCenterId: 'medical-center-2',
						nextAvailableDate: d1,
						nextAvailableTime: '09:45',
						latitude: -34.6149952,
						longitude: -58.4293087
					}
				},
				{
					meta: { acceptsPrivatePay: false, accepts: [] },
					result: {
						id: 'dr-fake-4-mc-3',
						doctorId: 'dr-fake-4',
						name: 'Dr. Ignacio López',
						image: '/images/doctor-icon.png',
						establishmentName: 'Hospital Central Norte',
						address: 'Monroe 3450, CABA',
						distanceInKm: undefined,
						price: '$8.300',
						copay: '$2.300',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 08:30`,
							`${day1} ${format(d1, 'dd')} - 13:15`,
							`${day2} ${format(d2, 'dd')} - 10:00`,
							`${day2} ${format(d2, 'dd')} - 17:45`
						],
						type: 'doctor',
						specialties: ['Cardiología'],
						medicalCenterId: 'medical-center-3',
						nextAvailableDate: d1,
						nextAvailableTime: '08:30',
						latitude: -34.5950,
						longitude: -58.3950
					}
				},
				{
					meta: { acceptsPrivatePay: true, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-5-mc-2',
						doctorId: 'dr-fake-5',
						name: 'Dra. Valentina Rossi',
						image: '/images/doctor-icon.png',
						establishmentName: 'Clínica San Martín',
						address: 'Av. Rivadavia 4521, CABA',
						distanceInKm: undefined,
						price: '$6.900',
						copay: '$1.800',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 10:10`,
							`${day1} ${format(d1, 'dd')} - 16:20`,
							`${day2} ${format(d2, 'dd')} - 11:35`,
							`${day2} ${format(d2, 'dd')} - 18:00`
						],
						type: 'doctor',
						specialties: ['Dermatología', 'Control'],
						medicalCenterId: 'medical-center-2',
						nextAvailableDate: d1,
						nextAvailableTime: '10:10',
						latitude: -34.6120,
						longitude: -58.4090
					}
				},
				{
					meta: { acceptsPrivatePay: false, accepts: [] },
					result: {
						id: 'dr-fake-6-mc-4',
						doctorId: 'dr-fake-6',
						name: 'Dr. Martín Cabrera',
						image: '/images/doctor-icon.png',
						establishmentName: 'Centro Médico Norte',
						address: 'Av. Cabildo 2200, CABA',
						distanceInKm: undefined,
						price: '$7.800',
						copay: '$2.100',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 12:00`,
							`${day1} ${format(d1, 'dd')} - 17:20`,
							`${day2} ${format(d2, 'dd')} - 09:50`,
							`${day2} ${format(d2, 'dd')} - 14:30`
						],
						type: 'doctor',
						specialties: ['Traumatología'],
						medicalCenterId: 'medical-center-4',
						nextAvailableDate: d1,
						nextAvailableTime: '12:00',
						latitude: -34.5700,
						longitude: -58.4400
					}
				},
				{
					meta: { acceptsPrivatePay: false, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-7-mc-5',
						doctorId: 'dr-fake-7',
						name: 'Dra. Lucía Fernández',
						image: '/images/doctor-icon.png',
						establishmentName: 'Consultorios Palermo',
						address: 'Honduras 5200, CABA',
						distanceInKm: undefined,
						price: '$5.200',
						copay: '$900',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 09:20`,
							`${day1} ${format(d1, 'dd')} - 15:05`,
							`${day2} ${format(d2, 'dd')} - 10:40`,
							`${day2} ${format(d2, 'dd')} - 16:10`
						],
						type: 'doctor',
						                        specialties: ['Ginecología'],
						medicalCenterId: 'medical-center-5',
						nextAvailableDate: d1,
						nextAvailableTime: '09:20',
						latitude: -34.5800,
						longitude: -58.4200
					}
				},
				{
					meta: { acceptsPrivatePay: true, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-8-mc-6',
						doctorId: 'dr-fake-8',
						name: 'Dr. Sebastián Martínez',
						image: '/images/doctor-icon.png',
						establishmentName: 'Centro Médico Belgrano',
						address: 'Av. Cabildo 1850, CABA',
						distanceInKm: undefined,
						price: '$7.500',
						copay: '$1.600',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 08:00`,
							`${day1} ${format(d1, 'dd')} - 14:30`,
							`${day2} ${format(d2, 'dd')} - 10:15`,
							`${day2} ${format(d2, 'dd')} - 16:45`
						],
						type: 'doctor',
						                        specialties: ['Urología'],
						medicalCenterId: 'medical-center-6',
						nextAvailableDate: d1,
						nextAvailableTime: '08:00',
						latitude: -34.5600,
						longitude: -58.4500
					}
				},
				{
					meta: { acceptsPrivatePay: true, accepts: [] },
					result: {
						id: 'dr-fake-9-mc-7',
						doctorId: 'dr-fake-9',
						name: 'Dra. Camila González',
						image: '/images/doctor-icon.png',
						establishmentName: 'Policlínico Villa Crespo',
						address: 'Av. Corrientes 6789, CABA',
						distanceInKm: undefined,
						price: '$6.200',
						copay: '$1.300',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 09:30`,
							`${day1} ${format(d1, 'dd')} - 15:45`,
							`${day2} ${format(d2, 'dd')} - 11:20`,
							`${day2} ${format(d2, 'dd')} - 15:30`
						],
						type: 'doctor',
						specialties: ['Oftalmología', 'Control'],
						medicalCenterId: 'medical-center-7',
						nextAvailableDate: d1,
						nextAvailableTime: '09:30',
						latitude: -34.6000,
						longitude: -58.4300
					}
				},
				{
					meta: { acceptsPrivatePay: false, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-10-mc-8',
						doctorId: 'dr-fake-10',
						name: 'Dr. Francisco Silva',
						image: '/images/doctor-icon.png',
						establishmentName: 'Hospital Italiano',
						address: 'Gascon 450, CABA',
						distanceInKm: undefined,
						price: '$8.900',
						copay: '$2.200',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 10:45`,
							`${day1} ${format(d1, 'dd')} - 16:00`,
							`${day2} ${format(d2, 'dd')} - 09:15`,
							`${day2} ${format(d2, 'dd')} - 14:30`
						],
						type: 'doctor',
						                        specialties: ['Gastroenterología'],
						medicalCenterId: 'medical-center-8',
						nextAvailableDate: d1,
						nextAvailableTime: '10:45',
						latitude: -34.6100,
						longitude: -58.4000
					}
				},
				{
					meta: { acceptsPrivatePay: true, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-11-mc-9',
						doctorId: 'dr-fake-11',
						name: 'Dra. Agustina Romero',
						image: '/images/doctor-icon.png',
						establishmentName: 'Centro Médico Recoleta',
						address: 'Av. Pueyrredón 1640, CABA',
						distanceInKm: undefined,
						price: '$6.800',
						copay: '$1.400',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 11:30`,
							`${day1} ${format(d1, 'dd')} - 17:15`,
							`${day2} ${format(d2, 'dd')} - 08:45`,
							`${day2} ${format(d2, 'dd')} - 15:20`
						],
						type: 'doctor',
						                        specialties: ['Endocrinología'],
						medicalCenterId: 'medical-center-9',
						nextAvailableDate: d1,
						nextAvailableTime: '11:30',
						latitude: -34.5900,
						longitude: -58.3900
					}
				},
				{
					meta: { acceptsPrivatePay: true, accepts: [] },
					result: {
						id: 'dr-fake-12-mc-10',
						doctorId: 'dr-fake-12',
						name: 'Dr. Nicolás Torres',
						image: '/images/doctor-icon.png',
						establishmentName: 'Sanatorio Güemes',
						address: 'Av. Córdoba 2678, CABA',
						distanceInKm: undefined,
						price: '$7.100',
						copay: '$1.750',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 12:15`,
							`${day1} ${format(d1, 'dd')} - 18:30`,
							`${day2} ${format(d2, 'dd')} - 09:40`,
							`${day2} ${format(d2, 'dd')} - 16:25`
						],
						type: 'doctor',
						specialties: ['Neurología', 'Clínica Médica', 'Neumonología'],
						medicalCenterId: 'medical-center-10',
						nextAvailableDate: d1,
						nextAvailableTime: '12:15',
						latitude: -34.6050,
						longitude: -58.4100
					}
				},
				{
					meta: { acceptsPrivatePay: false, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-13-mc-11',
						doctorId: 'dr-fake-13',
						name: 'Dra. Florencia Vega',
						image: '/images/doctor-icon.png',
						establishmentName: 'Centro de Salud Barracas',
						address: 'Av. Montes de Oca 1234, CABA',
						distanceInKm: undefined,
						price: '$5.500',
						copay: '$1.100',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 08:20`,
							`${day1} ${format(d1, 'dd')} - 13:50`,
							`${day2} ${format(d2, 'dd')} - 10:30`,
							`${day2} ${format(d2, 'dd')} - 17:10`
						],
						type: 'doctor',
						                        specialties: ['Psiquiatría'],
						medicalCenterId: 'medical-center-11',
						nextAvailableDate: d1,
						nextAvailableTime: '08:20',
						latitude: -34.6350,
						longitude: -58.3700
					}
				},
				{
					meta: { acceptsPrivatePay: true, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-14-mc-12',
						doctorId: 'dr-fake-14',
						name: 'Dr. Rodrigo Mendez',
						image: '/images/doctor-icon.png',
						establishmentName: 'Clínica Santa Isabel',
						address: 'Av. Las Heras 2890, CABA',
						distanceInKm: undefined,
						price: '$8.200',
						copay: '$2.000',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 07:45`,
							`${day1} ${format(d1, 'dd')} - 14:20`,
							`${day2} ${format(d2, 'dd')} - 11:10`,
							`${day2} ${format(d2, 'dd')} - 18:15`
						],
						type: 'doctor',
						                        specialties: ['Ortopedia'],
						medicalCenterId: 'medical-center-12',
						nextAvailableDate: d1,
						nextAvailableTime: '07:45',
						latitude: -34.5850,
						longitude: -58.4050
					}
				},
				{
					meta: { acceptsPrivatePay: true, accepts: [] },
					result: {
						id: 'dr-fake-15-mc-13',
						doctorId: 'dr-fake-15',
						name: 'Dra. Carolina Herrera',
						image: '/images/doctor-icon.png',
						establishmentName: 'Centro Médico Once',
						address: 'Av. Pueyrredón 545, CABA',
						distanceInKm: undefined,
						price: '$6.600',
						copay: '$1.350',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 09:10`,
							`${day1} ${format(d1, 'dd')} - 12:00`,
							`${day2} ${format(d2, 'dd')} - 10:00`,
							`${day2} ${format(d2, 'dd')} - 17:45`
						],
						type: 'doctor',
						specialties: ['Reumatología', 'Control'],
						medicalCenterId: 'medical-center-13',
						nextAvailableDate: d1,
						nextAvailableTime: '09:10',
						latitude: -34.6080,
						longitude: -58.4080
					}
				},
				{
					meta: { acceptsPrivatePay: false, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-16-mc-14',
						doctorId: 'dr-fake-16',
						name: 'Dr. Emiliano Castro',
						image: '/images/doctor-icon.png',
						establishmentName: 'Hospital Alemán',
						address: 'Av. Pueyrredón 1640, CABA',
						distanceInKm: undefined,
						price: '$9.100',
						copay: '$2.400',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 08:50`,
							`${day1} ${format(d1, 'dd')} - 16:30`,
							`${day2} ${format(d2, 'dd')} - 10:05`,
							`${day2} ${format(d2, 'dd')} - 15:50`
						],
						type: 'doctor',
						                        specialties: ['Neumología'],
						medicalCenterId: 'medical-center-14',
						nextAvailableDate: d1,
						nextAvailableTime: '08:50',
						latitude: -34.5900,
						longitude: -58.3900
					}
				},
				{
					meta: { acceptsPrivatePay: true, accepts: [{ name: 'OSDE', plans: ['210'] }] },
					result: {
						id: 'dr-fake-17-mc-15',
						doctorId: 'dr-fake-17',
						name: 'Dra. Julieta Navarro',
						image: '/images/doctor-icon.png',
						establishmentName: 'Consultorios Villa Urquiza',
						address: 'Av. Triunvirato 4235, CABA',
						distanceInKm: undefined,
						price: '$5.900',
						copay: '$1.250',
						appointments: [
							`${day1} ${format(d1, 'dd')} - 11:45`,
							`${day1} ${format(d1, 'dd')} - 17:50`,
							`${day2} ${format(d2, 'dd')} - 08:30`,
							`${day2} ${format(d2, 'dd')} - 14:40`
						],
						type: 'doctor',
						                        specialties: ['Hematología'],
						medicalCenterId: 'medical-center-15',
						nextAvailableDate: d1,
						nextAvailableTime: '11:45',
						latitude: -34.5700,
						longitude: -58.4800
					}
				}
			]

			// Filter candidates according to selection
			const filteredCandidates = candidates.filter(({ meta }) => {
				if (noInsurance) return meta.acceptsPrivatePay
				if (coverage) {
					return meta.accepts.some(a => a.name === coverage && (plan ? a.plans.includes(plan) : true))
				}
				// If no coverage selected and not private pay, show all for preview
				return true
			})

			const mock = filteredCandidates.map((c, index) => {
				const r = { ...c.result }
				
				// Calculate real distance if user location is available
				if (userLocation && r.latitude && r.longitude) {
					r.distanceInKm = calculateDistance(
						userLocation.latitude,
						userLocation.longitude,
						r.latitude,
						r.longitude
					);
				} else {
					// If no user location, remove distance to avoid showing incorrect data
					r.distanceInKm = undefined;
				}
				
				// Apply pricing visibility according to selection
				if (noInsurance) {
					r.copay = undefined
					// For specialty search, add priceLabel for testing different scenarios
					if (searchType?.toLowerCase() === "especialidad") {
						// Simulate different consultation types for testing
						if (index % 4 === 0) {
							// Dra. Valentina Rossi - has "Control" but no "Primera consulta" - should show no price
							r.priceLabel = undefined
							r.price = undefined
						} else if (index % 4 === 1) {
							// Dr. Mariana Pérez - has "Primera consulta" - should show price
							r.priceLabel = "Primera consulta"
							r.price = "$5.500"
						} else if (index % 4 === 2) {
							// Dr. Tomás Gutiérrez - has "Primera consulta" - should show price
							r.priceLabel = "Primera consulta"
							r.price = "$7.200"
						} else {
							// Dra. Sofía Morales - has "Primera consulta" - should show price
							r.priceLabel = "Primera consulta"
							r.price = "$5.800"
						}
					}
				} else if (coverage) {
					r.price = undefined
					// For specialty search with insurance, add priceLabel for testing different scenarios
					if (searchType?.toLowerCase() === "especialidad") {
						if (coverage === 'OSDE' && plan === '210') {
							// Simulate different copay scenarios for OSDE 210
							if (index % 3 === 0) {
								r.priceLabel = "Primera consulta con copago"
								r.copay = "$1.200"
							} else if (index % 3 === 1) {
								r.priceLabel = "Consulta general con copago"
								r.copay = "$800"
							} else {
								r.priceLabel = "Algunos estudios con copago"
								r.copay = undefined
							}
						} else if (coverage === 'Medicus') {
							// Simulate different copay scenarios for Medicus
							if (index % 3 === 0) {
								r.priceLabel = "Primera consulta con copago"
								r.copay = "$1.500"
							} else if (index % 3 === 1) {
								r.priceLabel = "Algunos estudios con copago"
								r.copay = undefined
							} else {
								// No copay at all - don't show anything
								r.priceLabel = undefined
								r.copay = undefined
							}
						} else {
							// Other coverages
							if (index % 2 === 0) {
								r.priceLabel = "Algunos estudios con copago"
								r.copay = undefined
							} else {
								// No copay at all - don't show anything
								r.priceLabel = undefined
								r.copay = undefined
							}
						}
					}
				}
				return r
			})

			setResults(mock)
			setTotalPages(Math.ceil(mock.length / resultsPerPage))
			setCurrentPage(1)
			setLoading(false)
			return
		}

        setResults(searchResults)
        setTotalPages(Math.ceil(searchResults.length / resultsPerPage))
        setCurrentPage(1) // Reset to first page on new search
        setLoading(false)
    }, [searchQuery, searchType, locationQuery, noInsurance, appointments, blockedSlots, coverage, plan, timeOfDay, isDoctorCoverageExcluded, resultsPerPage, isTimeInRange, calculateDistance, userLocation, locationLoading, locationError])

    // Filter out doctors with "Sin turnos disponibles" and update pagination
    useEffect(() => {
        // First, filter doctors that don't have available appointments for the selected time range
        const filtered = results.filter(result => {
            // Filter out results with "Sin turnos disponibles"
            if (result.appointments.length === 1 && result.appointments[0] === "Sin turnos disponibles") {
                return false;
            }

            // If timeOfDay filter is active, ensure there's at least one appointment in that time range
            if (timeOfDay !== 'all') {
                // Check if there's any appointment in the selected time range
                const hasAppointmentInTimeRange = result.appointments.some(appointment => {
                    if (appointment === "Sin turnos disponibles") return false;

                    // The format is like "Lun 15 - 14:30"
                    // First, split by "-" to get the time part
                    const parts = appointment.split('-');
                    if (parts.length < 2) return false;

                    // Get the time part and trim whitespace
                    const timePart = parts[1].trim();

                    // Now extract the hour from the time (format: "14:30")
                    const hour = parseInt(timePart.split(':')[0], 10);
                    if (isNaN(hour)) return false;

                    // Check if the appointment is in the selected time range
                    return isTimeInRange(hour, timeOfDay);
                });

                return hasAppointmentInTimeRange;
            }

            return true;
        });

        // Sort results based on the selected sort option
        const sortedResults = [...filtered].sort((a, b) => {
            // Apply different sorting logic based on sortBy parameter
            if (sortBy === "distance") {
                // Sort by distance if we have distance information
                if (a.distanceInKm !== undefined && b.distanceInKm !== undefined) {
                    const distanceComparison = a.distanceInKm - b.distanceInKm;

                    // If distances are equal, sort by name as secondary criteria
                    if (distanceComparison === 0) {
                        return a.name.localeCompare(b.name);
                    }

                    return distanceComparison;
                }
                // If we don't have distance for one or both, put the ones without distance at the end
                else if (a.distanceInKm === undefined && b.distanceInKm !== undefined) {
                    return 1; // a goes after b
                } else if (a.distanceInKm !== undefined && b.distanceInKm === undefined) {
                    return -1; // a goes before b
                }
                // If neither has distance, fall back to date sorting
            }

            // Default sorting by date (also fallback when distance sorting isn't possible)
            // If either result doesn't have a next available date, put it at the end
            if (!a.nextAvailableDate) return 1;
            if (!b.nextAvailableDate) return -1;

            // Compare dates first
            const dateComparison = a.nextAvailableDate.getTime() - b.nextAvailableDate.getTime();
            if (dateComparison !== 0) return dateComparison;

            // If dates are the same, compare times
            if (a.nextAvailableTime && b.nextAvailableTime) {
                // Extract hours and minutes for comparison
                const [aHours, aMinutes] = a.nextAvailableTime.split(':').map(Number);
                const [bHours, bMinutes] = b.nextAvailableTime.split(':').map(Number);

                // Compare hours
                if (aHours !== bHours) return aHours - bHours;

                // Compare minutes if hours are the same
                const minutesComparison = aMinutes - bMinutes;
                if (minutesComparison !== 0) return minutesComparison;
            }

            // If times are equal or we can't compare times, sort by name as secondary criteria
            return a.name.localeCompare(b.name);
        });

        // Compare current filtered results with previous to avoid unnecessary updates
        const currentResultsJSON = JSON.stringify(sortedResults);
        if (prevFilteredResultsRef.current !== currentResultsJSON) {
            setFilteredResults(sortedResults);
            prevFilteredResultsRef.current = currentResultsJSON;

            // Call the callback to pass results to parent component only when results actually change
            if (onResultsChange) {
                onResultsChange(sortedResults);
            }
        }

        // Update pagination
        const total = Math.ceil(sortedResults.length / resultsPerPage);
        setTotalPages(total);

        // If current page is now out of bounds, reset to page 1
        if (currentPage > total && total > 0) {
            setCurrentPage(1);
        }
    }, [results, timeOfDay, currentPage, resultsPerPage, coverage, plan, isTimeInRange, onResultsChange, sortBy]);

    // Get current page of results
    const currentResults = filteredResults.slice(
        (currentPage - 1) * resultsPerPage,
        currentPage * resultsPerPage
    );

    // Change page handler
    const handlePageChange = (pageNumber: number) => {
        // Ensure page number is within valid range
        if (pageNumber < 1 || pageNumber > totalPages) return
        setCurrentPage(pageNumber)

        // Scroll to top of page
        window.scrollTo({top: 0, behavior: 'smooth'})
    }

    // Format price with dots for thousands
    const formatPrice = (price: string): string => {
        if (!price || !price.startsWith('$')) return price;

        // Extract the numeric part
        const numericPart = price.substring(1).trim();

        // Convert to number and format
        const numValue = parseFloat(numericPart.replace(/\./g, '').replace(',', '.'));
        if (isNaN(numValue)) return price;

        // Format with thousands separator
        return `$${numValue.toLocaleString('es-AR', {minimumFractionDigits: 0}).replace(/,/g, '.')}`;
    }

    return (
        <div
            ref={containerRef}
            data-results-list="true"
            className="space-y-6 pb-24"
        >
            {loading ? (
                <div className="bg-white rounded-2xl md:rounded-lg border border-gray-100 shadow-sm p-8">
                    <div className="flex flex-col items-center justify-center">
                        <div className="relative">
                            <div
                                className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-blue-600"></div>
                            <div
                                className="absolute inset-0 rounded-full bg-gradient-to-tr from-blue-600/10 to-transparent"></div>
                        </div>
                        <p className="mt-6 text-gray-700 font-semibold">Buscando profesionales disponibles...</p>
                        <p className="mt-2 text-sm text-gray-500">Esto puede tomar unos segundos</p>
                    </div>
                </div>
            ) : !isFullySelected ? (
                <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl md:rounded-lg border border-amber-200 p-8">
                    <div className="flex flex-col items-center justify-center text-center">
                        <div className="w-16 h-16 bg-amber-100 rounded-xl flex items-center justify-center mb-6">
                            <AlertTriangle className="h-8 w-8 text-amber-600"/>
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3">
                            {isSelectionInProgress ? "Casi listo..." : "Seleccione su cobertura"}
                        </h3>
                        <p className="text-gray-600 max-w-md leading-relaxed">
                            {isSelectionInProgress
                                ? "Por favor seleccione un plan para completar la configuración y ver todos los profesionales disponibles"
                                : "Necesitamos conocer su cobertura médica para mostrarle los profesionales y precios correctos"}
                        </p>
                    </div>
                </div>
            ) : (sortBy === "distance" && locationLoading) ? (
                <div className="bg-white rounded-2xl md:rounded-lg border border-gray-100 shadow-sm p-8">
                    <div className="flex flex-col items-center justify-center">
                        <div className="relative">
                            <div
                                className="animate-spin rounded-full h-8 w-8 border-2 border-gray-200 border-t-blue-600"></div>
                        </div>
                        <p className="mt-4 text-gray-700 font-medium">Obteniendo su ubicación...</p>
                        <p className="mt-1 text-sm text-gray-500">Para ordenar por proximidad.</p>
                    </div>
                </div>
            ) : filteredResults.length === 0 ? (
                <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl md:rounded-lg border border-gray-200 p-8">
                    <div className="flex flex-col items-center justify-center text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mb-6">
                            <AlertTriangle className="h-8 w-8 text-gray-500"/>
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3">No encontramos resultados</h3>
                        <p className="text-gray-600 max-w-md leading-relaxed mb-6">
                            No hay profesionales disponibles con los filtros seleccionados. Intente ajustar la
                            cobertura, ubicación o horario.
                        </p>
                        <div className="flex flex-wrap gap-2 justify-center">
                            <span className="px-3 py-1.5 bg-white rounded-lg text-sm text-gray-600 border border-gray-300">Cambiar cobertura</span>
                            <span className="px-3 py-1.5 bg-white rounded-lg text-sm text-gray-600 border border-gray-300">Ampliar ubicación</span>
                            <span className="px-3 py-1.5 bg-white rounded-lg text-sm text-gray-600 border border-gray-300">Otros horarios</span>
                        </div>
                    </div>
                </div>
            ) : (
                <>
                    {/* Location Error Indicator - only show when sorting by distance */}
                    {sortBy === "distance" && locationError && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                            <div className="flex items-center text-sm text-blue-700">
                                <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                                <span>
                                    {locationError}. No se pueden mostrar las distancias para ordenar por proximidad.
                                </span>
                            </div>
                        </div>
                    )}
                    
                    {/* Results in Single Container */}
                    <div className="bg-white rounded-none md:rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                        {currentResults.map((result, index) => {
                            // Determine the display amount and label based on search type and coverage
                            let displayAmount: string | undefined = undefined;
                            let displayLabel: string | undefined = undefined;

                            // Use custom priceLabel if available (for specialty search)
                            if (result.priceLabel) {
                                displayLabel = result.priceLabel;
                                displayAmount = result.price || result.copay;
                            } else {
                                // Fallback to original logic
                                if (noInsurance) {
                                    if (searchType?.toLowerCase() === "estudio") {
                                        // For studies, show the specific study type and its price
                                        displayLabel = searchQuery;
                                        displayAmount = result.price;
                                    } else {
                                        // For non-study searches, only show price if "Primera consulta" is available AND has a price
                                        const hasPrimeraConsulta = result.specialties?.some(
                                            specialty => specialty === "Primera consulta"
                                        ) || result.type === "doctor";

                                        if (hasPrimeraConsulta && result.price) {
                                            displayLabel = "Primera consulta";
                                            displayAmount = result.price;
                                        }
                                        // If no "Primera consulta" or no price, don't show any price (both remain undefined)
                                    }
                                } else if (result.copay) {
                                    // For insurance, show copay as before
                                    displayLabel = "Copago";
                                    displayAmount = result.copay;
                                }
                            }

                            // Build the URL for direct booking
                            let bookingUrl = `/plataforma/reservar/cita?doctorId=${result.doctorId || result.id}`;

                            // Add medical center ID if available
                            if (result.medicalCenterId) {
                                bookingUrl += `&medicalCenterId=${result.medicalCenterId}`;
                            }

                            // Add date and time if available
                            if (result.nextAvailableDate && result.nextAvailableTime) {
                                const dateStr = format(result.nextAvailableDate, 'yyyy-MM-dd');
                                bookingUrl += `&date=${dateStr}&time=${result.nextAvailableTime}`;
                            }

                            // Use search as source instead of medicalCenter for proper back button navigation
                            bookingUrl += `&source=search&searchType=${encodeURIComponent(searchType.toLowerCase())}&q=${encodeURIComponent(searchQuery)}`;

                            // Add coverage parameters
                            if (noInsurance) {
                                bookingUrl += `&noCoverage=true`;
                            } else if (coverage) {
                                bookingUrl += `&coverage=${encodeURIComponent(coverage)}`;
                                if (plan) {
                                    bookingUrl += `&plan=${encodeURIComponent(plan)}`;
                                }
                            }

                            // Always pass consultation type parameter if search type is estudio
                            if (searchType?.toLowerCase() === "estudio") {
                                bookingUrl += `&consultationType=${encodeURIComponent(searchQuery)}`;
                            }

                            // Note: "profesional" search type is handled separately and not in this component

                            // Add time of day filter if selected
                            if (timeOfDay !== 'all') {
                                bookingUrl += `&timeOfDay=${encodeURIComponent(timeOfDay)}`;
                            }

                            return (
                                <div
                                    key={`${result.id}-${result.medicalCenterId}`}
                                    id={`result-${result.id}`}
                                    onMouseEnter={() => onItemHover && onItemHover(result.id)}
                                    onMouseLeave={() => onItemLeave && onItemLeave()}
                                    className={`${index !== currentResults.length - 1 ? 'border-b-[12px] border-slate-200' : ''}`}
                                >
                                    <div className={`px-4 md:px-5 py-8 md:py-7 ${highlightedResultId === result.id ? 'bg-slate-50' : 'bg-white'}`}>
                                        <div className="flex flex-col md:flex-row gap-5 md:gap-4 items-stretch">
                                            {/* Left: Doctor information */}
                                            <div className="flex-1 min-w-0 md:flex-[1_1_0%]">
                                                    <div className="flex items-start gap-4">
                                                    {/* Avatar */}
                                                    <div className="flex-shrink-0">
                                                            <div className="w-16 h-16 md:w-18 md:h-18 rounded-full bg-slate-50 border border-slate-200 flex items-center justify-center">
                                                            <img src="/images/turnera-logo-small.svg" alt="Turnera" className="h-8 w-8 md:h-9 md:w-9"/>
                                                        </div>
                                                    </div>
                                                    {/* Content */}
                                                    <div className="flex-1 min-w-0 flex flex-col">
                                                        {/* Header: name with improved typography */}
                                                        <div className="flex items-start justify-between gap-2">
                            <h3 className={`text-xl md:text-lg ${highlightedResultId === result.id ? 'font-bold' : 'font-semibold'} text-gray-900 leading-tight whitespace-normal`}>
                                                                {result.name}
                                                            </h3>
                                                        </div>

                                                        {/* Specialties */}
                                                        {result.specialties && result.specialties.length > 0 && (
                                                            <div className="-mt-0.5">
                                                                <ExpandableText
                                                                    text={result.specialties.join(", ")}
                                                                    maxLength={27}
                                                                    className="text-base md:text-[0.9rem] text-gray-700 font-normal"
                                                                />
                                                            </div>
                                                        )}

                                                        {/* Location + distance with improved spacing */}
                                                        <div className="mt-2">
                                                            <div className="flex text-gray-600 text-base md:text-sm leading-snug">
                                                                <Building2 className="h-4 w-4 text-gray-500 mr-1.5 flex-shrink-0 mt-1 md:mt-1.5"/>
                                                                <div className="min-w-0 whitespace-normal break-words">
                                                                    <div className="text-gray-900 font-medium text-lg md:text-[0.95rem]">{result.establishmentName}</div>
                                                                    <div className="text-gray-600 text-base md:text-sm">
                                                                        <ExpandableText
                                                                            text={result.address}
                                                                            maxLength={36}
                                                                            className="text-gray-600 text-base md:text-sm"
                                                                        />
                                                                        {result.distanceInKm !== undefined && (
                                                                            <div className="text-gray-500 text-sm mt-0.5">
                                                                                {result.distanceInKm < 1 ? `${Math.round(result.distanceInKm * 1000)} m` : `${result.distanceInKm.toFixed(1)} km`}
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                                                {/* Price/Copago badge with improved design */}
                        {displayLabel && (
                            <div className="mt-1.5">
                                <div
                                    className={`inline-flex flex-wrap items-center rounded-sm px-2.5 py-1 text-base md:text-sm font-medium ${
                                        noInsurance
                                            ? 'bg-[#ecf4fd] text-[#094CB1]'
                                            : 'bg-teal-50 text-teal-800'
                                    }`}
                                    aria-label={displayAmount ? `${displayLabel}: ${formatPrice(displayAmount)}` : displayLabel}
                                >
                                    <span className={`${noInsurance ? 'text-[#094CB1]' : 'text-teal-800'}`}>
                                        {displayLabel}
                                        {displayAmount && <span className="font-semibold">: {formatPrice(displayAmount)}</span>}
                                    </span>
                                </div>
                            </div>
                        )}
                                                    </div>
                                                </div>
                                            </div>

                        {/* Right: Availability and CTA */}
                                            <div className="shrink-0 md:w-[250px] lg:w-[270px] xl:w-[290px] flex flex-col gap-4 md:items-end">
                                                <div className="w-full">
                                                    <div className="text-gray-900 text-lg md:text-base font-medium mb-1.5">Próximos turnos</div>
                                                    <div className="h-[2px] bg-[#e3e5e6] mb-3"></div>
                                                    <div className="grid grid-cols-2 gap-3">
                                                        {result.appointments.slice(0, 4).map((appointment, index) => (
                                                            <button
                                                                key={`${result.id}-${result.medicalCenterId}-apt-${index}`}
                                                                 className="bg-[#ecf4fd] text-[#094CB1] border border-[#ecf4fd] hover:bg-blue-50 text-base md:text-sm font-medium rounded-sm px-3 py-2 transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/30"
                                                                aria-label={`Reservar ${appointment}`}
                                                                onClick={() => {
                                                                    const time = appointment.split('-')[1].trim();
                                                                    if (result.nextAvailableDate) {
                                                                        let timeBookingUrl = `/plataforma/reservar/cita?doctorId=${result.doctorId || result.id}`;
                                                                        if (result.medicalCenterId) {
                                                                            timeBookingUrl += `&medicalCenterId=${result.medicalCenterId}`;
                                                                        }
                                                                        const dateStr = format(result.nextAvailableDate, 'yyyy-MM-dd');
                                                                        timeBookingUrl += `&date=${dateStr}&time=${time}`;
                                                                        timeBookingUrl += `&source=search&searchType=${encodeURIComponent(searchType.toLowerCase())}&q=${encodeURIComponent(searchQuery)}`;
                                                                        if (noInsurance) {
                                                                            timeBookingUrl += `&noCoverage=true`;
                                                                        } else if (coverage) {
                                                                            timeBookingUrl += `&coverage=${encodeURIComponent(coverage)}`;
                                                                            if (plan) {
                                                                                timeBookingUrl += `&plan=${encodeURIComponent(plan)}`;
                                                                            }
                                                                        }
                                                                        if (searchType?.toLowerCase() === "estudio") {
                                                                            timeBookingUrl += `&consultationType=${encodeURIComponent(searchQuery)}`;
                                                                        }
                                                                        if (timeOfDay !== 'all') {
                                                                            timeBookingUrl += `&timeOfDay=${encodeURIComponent(timeOfDay)}`;
                                                                        }
                                                                        window.location.href = timeBookingUrl;
                                                                    }
                                                                }}
                                                            >
                                                                <div className="text-center">
                                                                    <span className="text-[#094CB1] font-semibold uppercase text-base md:text-sm">{appointment.split('-')[0].trim()}</span>
                                                                    <span className="text-gray-400 mx-1">•</span>
                                                                    <span className="text-[#094CB1] font-medium text-base md:text-sm">{appointment.split('-')[1].trim()}</span>
                                                                </div>
                                                            </button>
                                                        ))}
                                                    </div>
                                                </div>
                                                <Link href={bookingUrl} className="w-full md:w-full">
                                                    <Button className="w-full h-auto md:h-11 px-4 py-2.5 md:py-2 bg-[#0070F3] text-white hover:bg-[#0070F3]/90 border-0 text-lg md:text-base font-medium rounded-sm transition-all duration-200">
                                                        Ver todos los turnos
                                                    </Button>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    {/* Enhanced Pagination */}
                    <div className="bg-white rounded-none md:rounded-lg border border-gray-100 shadow-sm p-6">
                        <div className="flex justify-center items-center">
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handlePageChange(currentPage - 1)}
                                    disabled={currentPage === 1}
                                    className="h-10 w-10 p-0 rounded-lg hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 disabled:opacity-50 disabled:hover:bg-transparent disabled:hover:border-gray-200 disabled:hover:text-gray-400 border-gray-300"
                                >
                                    <ChevronLeft className="h-4 w-4"/>
                                </Button>

                                {/* Page numbers */}
                                <div className="flex items-center space-x-1">
                                    {Array.from({length: totalPages}, (_, i) => i + 1)
                                        .filter(page => {
                                            // Always show first and last page
                                            if (page === 1 || page === totalPages) return true;
                                            // Show pages near current page
                                            if (Math.abs(page - currentPage) <= 1) return true;
                                            return false;
                                        })
                                        .map((page, index, array) => {
                                            // Add ellipsis where needed
                                            if (index > 0 && array[index - 1] !== page - 1) {
                                                return (
                                                    <React.Fragment key={`ellipsis-${page}`}>
                                                        <span className="px-3 text-gray-400 font-medium">...</span>
                                                        <Button
                                                            key={page}
                                                            variant={currentPage === page ? "default" : "outline"}
                                                            size="sm"
                                                            onClick={() => handlePageChange(page)}
                                                            className={`h-10 w-10 p-0 rounded-lg font-semibold ${
                                                                currentPage === page
                                                                    ? "bg-blue-600 text-white hover:bg-blue-700"
                                                                    : "text-gray-600 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 border-gray-300"
                                                            }`}
                                                        >
                                                            {page}
                                                        </Button>
                                                    </React.Fragment>
                                                );
                                            }

                                            return (
                                                <Button
                                                    key={page}
                                                    variant={currentPage === page ? "default" : "outline"}
                                                    size="sm"
                                                    onClick={() => handlePageChange(page)}
                                                    className={`h-10 w-10 p-0 rounded-lg font-semibold ${
                                                        currentPage === page
                                                            ? "bg-blue-600 text-white hover:bg-blue-700"
                                                            : "text-gray-600 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 border-gray-300"
                                                    }`}
                                                >
                                                    {page}
                                                </Button>
                                            );
                                        })}
                                </div>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handlePageChange(currentPage + 1)}
                                    disabled={currentPage === totalPages}
                                    className="h-10 w-10 p-0 rounded-lg hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 disabled:opacity-50 disabled:hover:bg-transparent disabled:hover:border-gray-200 disabled:hover:text-gray-400 border-gray-300"
                                >
                                    <ChevronRight className="h-4 w-4"/>
                                </Button>
                            </div>
                        </div>

                        {/* Results Summary */}
                        <div className="text-center mt-4 mb-2">
                            <p className="text-sm text-gray-600">
                                Mostrando {((currentPage - 1) * resultsPerPage) + 1} - {Math.min(currentPage * resultsPerPage, filteredResults.length)} de {filteredResults.length} profesionales
                            </p>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
}

