"use client"

import { useEffect, useRef, useState } from "react"
import L from "leaflet"
import "leaflet/dist/leaflet.css"
import "leaflet.markercluster/dist/leaflet.markercluster.js"
import "leaflet.markercluster/dist/MarkerCluster.css"
import "leaflet.markercluster/dist/MarkerCluster.Default.css"
import { MedicalCenter } from "@/types/medical-center"
import { initialMedicalCenters as staticMedicalCenters } from "@/data/medicalCenters"
import { Result as FullResult } from "./SearchResultsList"
import { Button } from "@/components/ui/button"
import { Search } from "lucide-react"
import { Root } from "react-dom/client"

interface MapPoint {
  resultData: FullResult
  coords: [number, number]
}

interface GroupedMapPoint {
  coords: [number, number]
  results: FullResult[]
}

interface SearchResultsMapProps {
  results: FullResult[]
  onMarkerClick?: (id: string) => void
  center?: [number, number]
  zoom?: number
  searchQuery: string;
  searchType: string;
  noInsurance: boolean;
  coverage: string;
  plan: string;
  timeOfDay: string;
  // Add optional callback for search in area
  onSearchInArea?: (bounds: { north: number, south: number, east: number, west: number }) => void;
  // UX sync with list
  hoveredResultId?: string;
  onUnselect?: () => void;
  selectedResultId?: string;
  // Mobile-only: when true the inner map should stretch to fill the fixed container
  isMobileFullScreen?: boolean;
}

// Function to group map points by exact coordinates only
function groupMapPointsByExactCoordinates(mapPoints: MapPoint[]): GroupedMapPoint[] {
  // If there are no points or only one point, no grouping needed
  if (mapPoints.length <= 1) {
    return mapPoints.map(point => ({
      coords: point.coords,
      results: [point.resultData]
    }));
  }

  // Group exact same coordinates
  const exactGroupedMap = new Map<string, GroupedMapPoint>();

  mapPoints.forEach(point => {
    // Create a key from the coordinates with limited precision to group exact same points
    // Using 5 decimal places gives precision of about 1.1 meters
    const key = `${point.coords[0].toFixed(5)},${point.coords[1].toFixed(5)}`;

    if (exactGroupedMap.has(key)) {
      // Add to existing group
      const group = exactGroupedMap.get(key)!;
      group.results.push(point.resultData);
    } else {
      // Create new group
      exactGroupedMap.set(key, {
        coords: point.coords,
        results: [point.resultData]
      });
    }
  });

  // Convert map to array of groups
  return Array.from(exactGroupedMap.values());
}

export default function SearchResultsMap({
  results,
  onMarkerClick,
  center = [-34.603722, -58.381592],
  zoom = 12,
  searchQuery,
  searchType,
  noInsurance,
  coverage,
  plan,
  timeOfDay,
  onSearchInArea,
  hoveredResultId,
  onUnselect,
  selectedResultId,
  isMobileFullScreen = false,
}: SearchResultsMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<L.Map | null>(null)
  const markerClusterRef = useRef<L.MarkerClusterGroup | null>(null)
  const markersRef = useRef<L.Marker[]>([])
  const markerByIdRef = useRef<Map<string, L.Marker>>(new Map());
  const mapPointsRef = useRef<MapPoint[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [mobileMapHeight, setMobileMapHeight] = useState<number | null>(null)
  const popupRootsRef = useRef<Map<L.Marker, Root>>(new Map());
  const [mapPointsVersion, setMapPointsVersion] = useState(0); // State to trigger map update
  const [showSearchButton, setShowSearchButton] = useState(false);
  const unmountingRef = useRef(false); // Flag to track component unmounting state
  const isInitializingRef = useRef(false); // Flag to prevent parallel initialization attempts
  const onMarkerClickRef = useRef<SearchResultsMapProps['onMarkerClick'] | null>(null);

  // Desktop-only smooth scroll to corresponding list item
  const scrollToResultInList = (resultId: string) => {
    try {
      if (typeof window === 'undefined') return;
      if (window.innerWidth < 768) return; // desktop only

      // There may be two lists in DOM (mobile hidden + desktop visible) with duplicate IDs.
      // Find all matching elements and pick the visible one.
      const candidates = Array.from(document.querySelectorAll(`[id="result-${resultId}"]`)) as HTMLElement[];
      const target = candidates.find((el) => {
        try {
          const visible = el.offsetParent !== null || el.getClientRects().length > 0;
          // Ensure it's inside a visible list container
          const listContainer = el.closest('[data-results-list="true"]') as HTMLElement | null;
          const listVisible = !!listContainer && (listContainer.offsetParent !== null || listContainer.getClientRects().length > 0);
          return visible && listVisible;
        } catch { return false; }
      }) || candidates[0];

      if (target) {
        try { target.scrollIntoView({ behavior: 'smooth', block: 'center' }); } catch {}
      }
    } catch {}
  };

  // Store search parameters in a ref to avoid dependency issues
  const searchParamsRef = useRef({
    searchQuery,
    searchType,
    noInsurance,
    coverage,
    plan,
    timeOfDay
  });

  // Create a filter key to track filter changes
  const filterKey = `${searchQuery}-${searchType}-${noInsurance}-${coverage}-${plan}-${timeOfDay}`;
  const prevFilterKeyRef = useRef<string>(filterKey);

  // Debug output and filter change detection
  useEffect(() => {
    // console.log("SearchResultsMap received results (full):", results);
    // console.log("Search context:", { searchQuery, searchType, noInsurance, coverage, plan, timeOfDay });

    // Check if filters have changed
    if (filterKey !== prevFilterKeyRef.current) {
      // Clean up existing map instance when filters change
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        } catch (error) {
          console.warn('Error removing map instance during filter change:', error);
        }
      }

      // Update the previous filter key
      prevFilterKeyRef.current = filterKey;
    }
  }, [filterKey, results, searchQuery, searchType, noInsurance, coverage, plan, timeOfDay]);

  // Process results to create map points
  useEffect(() => {
    const processMapPoints = () => {
      if (isInitializingRef.current) return;

      setIsLoading(true);
      isInitializingRef.current = true;

      const medicalCenters = staticMedicalCenters;
      const immediatePoints: MapPoint[] = [];

      for (const result of results) {
        let coords: [number, number] | null = null;

        // First, try to get coordinates directly from the result
        if (result.latitude && result.longitude) {
          coords = [result.latitude, result.longitude];
        }
        // Fallback to medical center location if result doesn't have coordinates
        else if (result.medicalCenterId) {
          const medicalCenter = medicalCenters.find(mc => mc.id === result.medicalCenterId);
          if (medicalCenter?.location?.latitude && medicalCenter.location?.longitude) {
            coords = [medicalCenter.location.latitude, medicalCenter.location.longitude];
          }
        }

        if (coords) {
          immediatePoints.push({ resultData: result, coords });
        } else {
          // Only fall back to geocoding if we have no coordinates at all
          // This should be rare now that we have coordinates in the results
          console.warn(`No coordinates found for result ${result.id}, skipping map point`);
        }
      }

      // Set map points and finish loading immediately since we have coordinates
      mapPointsRef.current = immediatePoints;
      setMapPointsVersion(prev => prev + 1);
      setIsLoading(false);
      isInitializingRef.current = false;
    };

    if (results.length > 0) {
      processMapPoints();
    } else {
      mapPointsRef.current = [];
      setIsLoading(false);
      setMapPointsVersion(prev => prev + 1);
    }

    return () => {
      isInitializingRef.current = false;
    };
  }, [results]);

  // Update search params ref when props change
  useEffect(() => {
    searchParamsRef.current = {
      searchQuery,
      searchType,
      noInsurance,
      coverage,
      plan,
      timeOfDay
    };
  }, [searchQuery, searchType, noInsurance, coverage, plan, timeOfDay]);

  // Keep latest click handler in a ref to avoid re-binding map on each render
  useEffect(() => {
    onMarkerClickRef.current = onMarkerClick;
  }, [onMarkerClick]);

  // Initialize and update map - runs when mapPointsVersion changes
  useEffect(() => {

    // Prevent initialization if already in progress
    if (isInitializingRef.current) return;

    // Wrap in try/catch to handle any potential errors
    try {
      // Check if map container is valid before proceeding
      if (!mapRef.current || !document.body.contains(mapRef.current)) {
        // Retry once on next tick to avoid race with initial render
        setTimeout(() => setMapPointsVersion(prev => prev + 1), 0)
        return;
      }

      // Get current map points
      const mapPoints = mapPointsRef.current;

      // Only clean up existing map instance if absolutely necessary
      const needsReset = mapInstanceRef.current && (
        !mapInstanceRef.current.getContainer() ||
        !document.body.contains(mapInstanceRef.current.getContainer()) ||
        // Only reset if there's a significant mismatch between markers and points
        (mapPoints.length > 0 && markersRef.current.length === 0) ||
        (mapPoints.length === 0 && markersRef.current.length > 0)
      );

      if (needsReset && mapInstanceRef.current) {
        // Clean up existing map instance
        try {
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
          markerClusterRef.current = null;
        } catch (error) {
          console.warn('Error removing map instance during transition:', error);
        }
      }
    } catch (error) {
      console.error('Error in map initialization effect:', error);
    }

    // Initialize map if it doesn't exist or if the map container has changed
    if ((!mapInstanceRef.current || !mapInstanceRef.current.getContainer()) && mapRef.current) {
      // Set initializing flag to prevent parallel initialization
      isInitializingRef.current = true;

              // Initialize map instance without attribution
        try {
          mapInstanceRef.current = L.map(mapRef.current, {
            zoomControl: false,
            attributionControl: false, // Completely disable attribution control
            minZoom: 12 // Prevent zooming out too far (approximately 1km view)
          }).setView(center, zoom);

        // Add tile layer without attribution
        L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
          attribution: '', // Empty attribution string
          subdomains: 'abcd',
          maxZoom: 19,
          crossOrigin: true as any
        }).addTo(mapInstanceRef.current);

        // Add zoom control to bottom right
        L.control.zoom({ position: 'bottomright' }).addTo(mapInstanceRef.current);

        // Initialize marker cluster group
        markerClusterRef.current = L.markerClusterGroup({
          maxClusterRadius: 40, // Distance in pixels within which markers will be clustered
          spiderfyOnMaxZoom: true, // When clicking a cluster at the final zoom level, spiderfy it
          showCoverageOnHover: false, // Don't show the area that will be clustered
          zoomToBoundsOnClick: true, // When clicking a cluster, zoom to its bounds
          disableClusteringAtZoom: 18, // At zoom level 18+, disable clustering
          iconCreateFunction: (cluster) => {
            // Spatial clusters (nearby but not identical coordinates)
            const count = cluster.getChildCount();
            return L.divIcon({
              className: 'custom-map-marker-group',
              html: `
                <div class="cluster-badge" title="Varios profesionales" aria-label="Varios profesionales">
                  <span class="cluster-text">${count}</span>
                </div>
              `,
              iconSize: [30, 30],
              iconAnchor: [15, 15],
              popupAnchor: [0, -15]
            });
          }
        });

        // Add the marker cluster group to the map
        mapInstanceRef.current.addLayer(markerClusterRef.current);

        // Add event listeners for map movement
        mapInstanceRef.current.on('moveend', () => {
          setShowSearchButton(true);
        });

        // Reset initializing flag
        isInitializingRef.current = false;
      } catch (error) {
        console.warn('Error initializing map:', error);
        // If there was an error initializing the map, make sure we clean up
        if (mapInstanceRef.current) {
          try {
            mapInstanceRef.current.remove();
          } catch {
            // Ignore cleanup errors
          }
          mapInstanceRef.current = null;
        }
        isInitializingRef.current = false;
      }
    }

    const mapInstance = mapInstanceRef.current;
    if (!mapInstance) {
      return; // Don't try to recreate the map in this render cycle
    }

    // Ensure the map container is valid
    if (!mapInstance.getContainer() || !document.body.contains(mapInstance.getContainer())) {
      try {
        mapInstance.remove();
      } catch (error) {
        console.warn('Error removing invalid map instance:', error);
      }
      mapInstanceRef.current = null;
      return;
    }

    // 1. Clear the marker cluster group and clean up React roots
    if (markerClusterRef.current) {
      try {
        // Clean up React roots for existing markers
        markersRef.current.forEach(marker => {
          const root = popupRootsRef.current.get(marker);
          if (root) {
            try {
              root.unmount();
            } catch (error) {
              console.warn('Error unmounting popup root during map update:', error);
            }
            popupRootsRef.current.delete(marker);
          }
        });

        // Clear all markers from the cluster group
        markerClusterRef.current.clearLayers();
        markersRef.current = [];
        markerByIdRef.current.clear();
      } catch (error) {
        console.warn('Error clearing marker cluster:', error);
      }
    }

    // Function to create a marker icon based on count
    const createMarkerIcon = (count: number = 1) => {
      // Single marker: compact solid circle with white ring and center dot (no pointer)
      if (count === 1) {
        return L.divIcon({
          className: 'custom-map-marker',
          html: `
            <div class="marker-container">
              <div class="pin-dot">
                <div class="pin-center-dot"></div>
              </div>
            </div>
          `,
          iconSize: [22, 22],
          iconAnchor: [11, 11],
          popupAnchor: [0, -11]
        });
      }

      // Grouped marker at the SAME exact coordinates (medical center grouping)
      // Distinct from spatial clusters created by MarkerClusterGroup
      return L.divIcon({
        className: 'custom-map-marker-center-group',
        html: `
          <div class="marker-container-group" title="Establecimiento" aria-label="Establecimiento">
            <div class="center-group-circle">
              <svg class="center-group-hospital" viewBox="0 0 24 24" width="16" height="16" aria-hidden="true">
                <path d="M10 4h4v6h6v4h-6v6h-4v-6H4v-4h6z" fill="#0070F3" fill-opacity="0.18"></path>
              </svg>
              <span class="center-group-text">${count}</span>
            </div>
          </div>
        `,
        iconSize: [28, 28],
        iconAnchor: [14, 14],
        popupAnchor: [0, -14]
      });
    };

    // 2. Add new markers
    const mapPoints = mapPointsRef.current;

    if (mapPoints.length > 0 && markerClusterRef.current && mapInstanceRef.current) {
      try {
        // Group by exact coordinates for efficiency
        const groupedPoints = groupMapPointsByExactCoordinates(mapPoints);
        const bounds = L.latLngBounds(groupedPoints.map((gp: GroupedMapPoint) => gp.coords));

      // Format price with dot as thousands separator
      const formatPrice = (price: string) => {
        if (!price) return price;
        // Remove any existing formatting and currency symbols
        const numericValue = price.replace(/[^\d,]/g, '');
        // If it contains a comma (decimal separator), handle it separately
        if (numericValue.includes(',')) {
          const [intPart, decPart] = numericValue.split(',');
          // Format the integer part with dots as thousands separators
          const formattedInt = intPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
          return `$${formattedInt},${decPart}`;
        } else {
          // Format with dots as thousands separators
          return `$${numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}`;
        }
      };

      // Create markers and add them to the cluster group
      groupedPoints.forEach((groupedPoint: GroupedMapPoint) => {
        // Create marker with count for grouped points
        const count = groupedPoint.results.length;
        const marker = L.marker(groupedPoint.coords, {
          icon: createMarkerIcon(count)
        });

        let popupContent = '';

        // Create different popup content based on count
        if (count === 1) {
          // For single points, use the original popup format
          const result = groupedPoint.results[0];

          popupContent = `
            <div class="map-popup-container">
              <div class="w-72 p-3 bg-white rounded-lg shadow-sm text-sm font-sans">
                <div class="flex justify-between items-start mb-2">
                  <h3 class="font-bold text-[#1c2533] text-base mr-2 leading-tight">
                    ${result.name}
                  </h3>
                  ${result.price ?
                    `<div class="bg-[#0070F3]/10 text-[#0070F3] px-1.5 py-0.5 rounded text-[10px] font-semibold flex-shrink-0 whitespace-nowrap">
                      Precio: ${formatPrice(result.price)}
                    </div>` : ''
                  }
                </div>

                <div class="flex items-start text-gray-600 mb-2.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1.5 flex-shrink-0 mt-0.5" style="min-width: 14px;">
                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>
                  <span class="text-sm leading-snug">${result.address}</span>
                </div>

                <div class="mb-3">
                  <h4 class="text-sm font-medium text-[#1c2533] mb-1.5">Próximos turnos</h4>
                  <div class="grid grid-cols-2 gap-1.5">
                    ${result.appointments.slice(0, 2).map((apt: string, index: number) => {
                      // Calculate appointment date and time for URL
                      const appointmentDate = result.nextAvailableDate ? new Date(result.nextAvailableDate) : new Date();
                      let timeForUrl = "";
                      const parts = apt.split(' - '); // e.g., ["Lun 15", "14:30"]

                      if (parts.length === 2) {
                        timeForUrl = parts[1].trim(); // "14:30"
                        const dayParts = parts[0].split(' '); // e.g., ["Lun", "15"]
                        if (dayParts.length === 2) {
                          const dayInMonth = parseInt(dayParts[1], 10);
                          if (!isNaN(dayInMonth) && result.nextAvailableDate) {
                            const firstAppointmentDay = new Date(result.nextAvailableDate).getDate();
                            if (dayInMonth < firstAppointmentDay && index > 0) {
                              appointmentDate.setMonth(appointmentDate.getMonth() + 1);
                            }
                            appointmentDate.setDate(dayInMonth);
                          }
                        }
                      } else if (result.nextAvailableDate) {
                        timeForUrl = result.nextAvailableTime || "";
                      }

                      // Format date for URL
                      const dateForUrl = appointmentDate.toISOString().split('T')[0]; // yyyy-MM-dd

                      // Build booking URL
                      let slotBookingUrl = `/plataforma/reservar/cita?doctorId=${result.doctorId || result.id}`;
                      if (result.medicalCenterId) slotBookingUrl += `&medicalCenterId=${result.medicalCenterId}`;
                      slotBookingUrl += `&date=${dateForUrl}&time=${encodeURIComponent(timeForUrl)}`;
                      if (noInsurance) slotBookingUrl += `&noCoverage=true`;
                      else if (coverage) {
                        slotBookingUrl += `&coverage=${encodeURIComponent(coverage)}`;
                        if (plan) slotBookingUrl += `&plan=${encodeURIComponent(plan)}`;
                      }
                      if (searchType.toLowerCase() === "estudio") slotBookingUrl += `&consultationType=${encodeURIComponent(searchQuery)}`;
                      // Add source parameter to match SearchResultsList
                      slotBookingUrl += `&source=search&searchType=${encodeURIComponent(searchType.toLowerCase())}&q=${encodeURIComponent(searchQuery)}`;

                      return `<a href="${slotBookingUrl}" class="block h-full">
                        <div class="bg-white text-blue-700 border border-blue-200 hover:bg-blue-50 text-xs font-medium rounded-sm px-2 py-1.5 transition-colors duration-200 cursor-pointer">
                          <div class="text-center">
                            <span class="inline-flex items-center gap-1 whitespace-nowrap"><span class="text-blue-700 font-medium uppercase">${apt.split('-')[0].trim()}</span><span class="text-blue-700 font-semibold">${apt.split('-')[1].trim()}</span></span>
                          </div>
                        </div>
                      </a>`;
                    }).join('')}
                    ${result.appointments.length === 0 ?
                      '<p class="text-xs text-gray-400 italic">No hay turnos próximos.</p>' : ''
                    }
                  </div>
                </div>

                <a href="/plataforma/reservar/cita?doctorId=${result.doctorId || result.id}${result.medicalCenterId ? `&medicalCenterId=${result.medicalCenterId}` : ''}${noInsurance ? '&noCoverage=true' : ''}${coverage ? `&coverage=${encodeURIComponent(coverage)}${plan ? `&plan=${encodeURIComponent(plan)}` : ''}` : ''}${searchType.toLowerCase() === "estudio" ? `&consultationType=${encodeURIComponent(searchQuery)}` : ''}&source=search&searchType=${encodeURIComponent(searchType.toLowerCase())}&q=${encodeURIComponent(searchQuery)}"
                   class="block">
                  <button class="w-full h-10 px-4 py-2 bg-[#0070F3] text-white hover:bg-[#0070F3]/90 border-0 text-sm font-medium rounded-sm">
                    Ver todos los turnos
                  </button>
                </a>
              </div>
            </div>
          `;

          // (single-point click handled by the generic click handler below)
        } else {
          // For grouped points, create a list of doctors
          const location = groupedPoint.results[0].establishmentName; // Get medical center name

          popupContent = `
            <div class="map-popup-container">
              <div class="w-72 p-3 bg-white rounded-lg shadow-sm text-sm font-sans">
                <div class="mb-2">
                  <h3 class="font-bold text-[#1c2533] text-base leading-tight">
                    ${location}
                  </h3>
                </div>

                <div class="flex items-start text-gray-600 mb-2.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1.5 flex-shrink-0 mt-0.5" style="min-width: 14px;">
                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>
                  <span class="text-sm leading-snug">${groupedPoint.results[0].address}</span>
                </div>

                <div class="mb-3">
                  <h4 class="text-sm font-medium text-[#1c2533] mb-1.5">Profesionales disponibles</h4>

                  <div class="overflow-y-auto max-h-[150px] pr-1">
                    ${groupedPoint.results.slice(0, 5).map((result: FullResult) => {
                      // Get first available appointment if any
                      const firstAppointment = result.appointments.length > 0 ? result.appointments[0] : null;

                      // Create URL for this doctor
                      const doctorUrl = `/plataforma/reservar/cita?doctorId=${result.doctorId || result.id}${result.medicalCenterId ? `&medicalCenterId=${result.medicalCenterId}` : ''}${noInsurance ? '&noCoverage=true' : ''}${coverage ? `&coverage=${encodeURIComponent(coverage)}${plan ? `&plan=${encodeURIComponent(plan)}` : ''}` : ''}${searchType.toLowerCase() === "estudio" ? `&consultationType=${encodeURIComponent(searchQuery)}` : ''}&source=search&searchType=${encodeURIComponent(searchType.toLowerCase())}&q=${encodeURIComponent(searchQuery)}`;

                      return `
                        <div class="border border-[#0070F3]/20 rounded-lg p-2 mb-2 hover:bg-[#0070F3]/5 transition-colors cursor-pointer" onclick="window.location.href='${doctorUrl}'">
                          <div class="flex justify-between items-start">
                            <div class="font-medium text-[#1c2533] text-sm">${result.name}</div>
                            ${result.price ?
                              `<span class="inline-flex items-center rounded-sm px-2.5 py-1 text-[10px] font-medium bg-blue-50 text-blue-700 whitespace-nowrap">${formatPrice(result.price)}</span>` :
                              ''
                            }
                          </div>
                          <div class="mt-1 text-xs text-gray-600">
                            ${firstAppointment ? `
                              <span class="text-gray-600 mr-1">Próximo:</span><span class="inline-flex items-center gap-1 whitespace-nowrap"><span class="text-blue-700 font-medium uppercase">${firstAppointment.split('-')[0].trim()}</span><span class="text-blue-700 font-semibold">${firstAppointment.split('-')[1].trim()}</span></span>
                            ` : `<span class="text-xs text-gray-400 italic">No hay turnos próximos</span>`}
                          </div>
                        </div>
                      `;
                    }).join('')}
                    ${groupedPoint.results.length > 5 ?
                      `<div class="text-center text-xs text-gray-500 mt-1">
                        +${groupedPoint.results.length - 5} profesionales más
                      </div>` : ''
                    }
                  </div>
                </div>

                <a href="/plataforma/reservar/cm/mc-${groupedPoint.results[0].medicalCenterId ? (groupedPoint.results[0].medicalCenterId.startsWith('mc-') ? groupedPoint.results[0].medicalCenterId.substring(3) : groupedPoint.results[0].medicalCenterId) : ''}?source=map${noInsurance ? '&noCoverage=true' : ''}${coverage ? `&coverage=${encodeURIComponent(coverage)}${plan ? `&plan=${encodeURIComponent(plan)}` : ''}` : ''}${searchType.toLowerCase() === "estudio" ? `&consultationType=${encodeURIComponent(searchQuery)}` : ''}"
                   class="block">
                  <button class="w-full h-10 px-4 py-2 bg-[#0070F3] text-white hover:bg-[#0070F3]/90 border-0 text-sm font-medium rounded-sm">Ver todos los profesionales</button>
                </a>
              </div>
            </div>
          `;
        }

        // Bind the popup with the HTML content
        marker.bindPopup(popupContent, {
          minWidth: 288,
          maxWidth: 300,
          offset: [0, -10],
          autoPan: true,
          closeButton: false
        });

        // Add popupopen event to center the map with an offset and notify selection
        marker.on('popupopen', () => {
          try {
            const firstId = (count === 1 ? groupedPoint.results[0]?.id : groupedPoint.results[0]?.id);
            if (firstId) onMarkerClickRef.current?.(firstId);
          } catch {}
          // Get the popup's position
          const pos = marker.getLatLng();

          // Center the map with an offset to show the popup in the lower center
          // This makes the popup more visible by positioning it higher in the viewport
          if (mapInstanceRef.current) {
            // Calculate the offset (move the center point up by 25% of the map height)
            const offset = [0, -mapInstanceRef.current.getSize().y * 0.25];

            // Get pixel coordinates for the marker
            const px = mapInstanceRef.current.project(pos);

            // Apply the offset to the pixel coordinates
            px.y += offset[1];

            // Convert back to geographic coordinates and pan to that point
            const latlng = mapInstanceRef.current.unproject(px);
            mapInstanceRef.current.panTo(latlng);
          }
        });

        marker.on('popupclose', () => {
          // Skip if component is unmounting - cleanup will happen in the unmount effect
          if (unmountingRef.current) return;

          const root = popupRootsRef.current.get(marker);
          if (root) {
            try {
              root.unmount();
            } catch (error) {
              console.warn('Error unmounting popup root on popup close:', error);
            }
            popupRootsRef.current.delete(marker);
          }
        });

        // Add the marker to our reference array
        markersRef.current.push(marker);
        // Map marker by each contained result id for quick lookup
        groupedPoint.results.forEach((r: FullResult) => {
          markerByIdRef.current.set(r.id, marker);
        });

        // Add the marker to the cluster group
        markerClusterRef.current?.addLayer(marker);

        // Ensure popup opens on first click and notify parent selection
        marker.on('click', (e: any) => {
          try {
            if (e?.originalEvent) {
              e.originalEvent.preventDefault?.();
              e.originalEvent.stopPropagation?.();
            }
          } catch {}
          try { marker.openPopup(); } catch {}
          const firstId = groupedPoint.results[0]?.id;
          if (firstId) onMarkerClickRef.current?.(firstId);
          // Ask the list to navigate to the correct paginated page for this result
          try { window.dispatchEvent(new CustomEvent('navigate-to-result', { detail: { id: firstId } })); } catch {}
          // Smooth scroll to the corresponding list result on desktop
          if (firstId) scrollToResultInList(firstId);
        });

        // Notify when popup closes to clear selection
        marker.on('popupclose', () => {
          if (onUnselect) onUnselect();
        });
      });

        // Fit bounds to show all markers if there are any
        if (bounds.isValid()) {
          mapInstanceRef.current.fitBounds(bounds, { padding: [50, 50], maxZoom: 15 });
        }
        try {
          // Notify list that map finished rendering markers for current filters
          window.dispatchEvent(new CustomEvent('map-ready', { detail: { key: filterKey } }))
        } catch {}
      } catch (error) {
        console.error('Error adding markers:', error);
      }
    }
    else {
      // Even with no markers, consider the map ready for current filters
      try {
        window.dispatchEvent(new CustomEvent('map-ready', { detail: { key: filterKey } }))
      } catch {}
    }

    // Cleanup function for the map effect
    return () => {
      // We don't destroy the map here, just leave cleanup for component unmount
    };
    
  }, [mapPointsVersion]);

  // Ensure map initialization after loading finishes (avoids race where container isn't mounted yet)
  useEffect(() => {
    if (!isLoading) {
      setMapPointsVersion(prev => prev + 1)
    }
  }, [isLoading])

  // Mobile: make the map container fill the remaining viewport height to touch the footer
  useEffect(() => {
    const updateMobileHeight = () => {
      try {
        if (typeof window === 'undefined') return;
        const isMobile = window.innerWidth < 768;
        if (!isMobile) { setMobileMapHeight(null); return; }
        const el = containerRef.current;
        if (!el) return;
        const rect = el.getBoundingClientRect();
        const safeInset = (window as any).visualViewport ? Math.max(0, ((window as any).visualViewport.height || window.innerHeight) - window.innerHeight) : 0;
        const height = Math.max(300, Math.round(window.innerHeight - rect.top - safeInset));
        setMobileMapHeight(height);
      } catch {}
    };
    updateMobileHeight();
    window.addEventListener('resize', updateMobileHeight);
    window.addEventListener('orientationchange', updateMobileHeight as any);
    return () => {
      window.removeEventListener('resize', updateMobileHeight);
      window.removeEventListener('orientationchange', updateMobileHeight as any);
    };
  }, []);

  // React to hoveredResultId: focus and enlarge marker
  useEffect(() => {
    const hoveredId = hoveredResultId;
    const map = mapInstanceRef.current;
    if (!map) return;
    // If a selection is active, ignore hover-driven panning
    if (selectedResultId) return;
    // Reset all markers DOM transform first
    markersRef.current.forEach((m) => {
      const iconEl = (m as any)._icon as HTMLElement | undefined;
      if (iconEl) {
        // Reset inner container transform so Leaflet's outer transform isn't affected
        const innerSingle = iconEl.querySelector('.marker-container') as HTMLElement | null;
        const innerGroup = iconEl.querySelector('.marker-container-group') as HTMLElement | null;
        if (innerSingle) {
          innerSingle.style.transform = 'translateY(0)';
          innerSingle.style.transition = '';
          innerSingle.style.transformOrigin = '';
        }
        if (innerGroup) {
          innerGroup.style.transform = '';
          innerGroup.style.transition = '';
          innerGroup.style.transformOrigin = '';
        }
        try { m.setZIndexOffset(0); } catch {}
      }
    });
    // Also reset any visible cluster icons (darker blue squircle)
    try {
      const container = map.getContainer();
      if (container) {
        const clusters = container.querySelectorAll('.cluster-badge');
        clusters.forEach((el: any) => {
          (el as HTMLElement).style.transform = '';
          (el as HTMLElement).style.transition = '';
          (el as HTMLElement).style.transformOrigin = '';
        });
      }
    } catch {}

    if (!hoveredId) return;
    const marker = markerByIdRef.current.get(hoveredId);
    if (!marker) return;
    try {
      // If the marker is currently clustered, highlight the visible parent cluster icon
      const clusterGroup = markerClusterRef.current as any;
      const visibleParent = clusterGroup?.getVisibleParent?.(marker) || null;
      if (visibleParent && visibleParent !== marker) {
        const clusterIconEl = (visibleParent as any)._icon as HTMLElement | undefined;
        if (clusterIconEl) {
          const badge = clusterIconEl.querySelector('.cluster-badge') as HTMLElement | null;
          if (badge) {
            badge.style.transition = 'transform 200ms ease-out';
            badge.style.transformOrigin = 'center center';
            badge.style.transform = 'scale(1.25)';
          }
          try { (visibleParent as any).setZIndexOffset?.(2000); } catch {}
        }
        // Recalculate map size before panning so center reflects current visible size
        try { map.invalidateSize?.(); } catch {}
        const parentLatLng = (visibleParent as any).getLatLng?.();
        if (parentLatLng) {
          requestAnimationFrame(() => {
            try { map.panTo(parentLatLng, { animate: true }); } catch {}
          });
        }
        return; // Cluster handled, skip single/group pin handling
      }
      // Slightly enlarge the marker element
      const iconEl = (marker as any)._icon as HTMLElement | undefined;
      if (iconEl) {
        // Scale inner content, not the Leaflet wrapper (which Leaflet rewrites on pan/zoom)
        const innerSingle = iconEl.querySelector('.marker-container') as HTMLElement | null;
        const innerGroup = iconEl.querySelector('.marker-container-group') as HTMLElement | null;
        if (innerSingle) {
          innerSingle.style.transition = 'transform 200ms ease-out';
          innerSingle.style.transformOrigin = 'center center';
          innerSingle.style.transform = 'translateY(0) scale(1.6)';
        }
        if (innerGroup) {
          innerGroup.style.transition = 'transform 200ms ease-out';
          innerGroup.style.transformOrigin = 'center center';
          innerGroup.style.transform = 'scale(1.4)';
        }
        try { (marker as any).setZIndexOffset?.(2000); } catch {}
      }
      // Recalculate map size before panning so center reflects current visible size
      try { map.invalidateSize?.(); } catch {}

      // Pan to the marker after a frame to ensure size invalidation has applied
      const latlng = (marker as any).getLatLng?.();
      if (latlng) {
        requestAnimationFrame(() => {
          try { map.panTo(latlng, { animate: true }); } catch {}
        });
      }
    } catch {}
  }, [hoveredResultId]);

  // Keep Leaflet's internal size in sync with container resizes (desktop map height changes)
  useEffect(() => {
    const mapEl = mapRef.current;
    const map = mapInstanceRef.current;
    if (!mapEl || !map) return;

    let rafId = 0 as number | undefined as any;
    const invalidate = () => {
      try { if (rafId) cancelAnimationFrame(rafId as any); } catch {}
      rafId = requestAnimationFrame(() => {
        try { map.invalidateSize?.(); } catch {}
      });
    };

    // Observe the actual Leaflet container element for size changes
    const ro = new ResizeObserver(() => invalidate());
    try { ro.observe(mapEl); } catch {}

    // Also listen to window resize/orientation changes as a fallback
    const onWindow = () => invalidate();
    window.addEventListener('resize', onWindow);
    window.addEventListener('orientationchange', onWindow as any);

    return () => {
      try { ro.disconnect(); } catch {}
      try { if (rafId) cancelAnimationFrame(rafId as any); } catch {}
      window.removeEventListener('resize', onWindow);
      window.removeEventListener('orientationchange', onWindow as any);
    };
  }, [mapPointsVersion]);

  // Cleanup on component unmount
  useEffect(() => {
    // Capture the current value of the refs at effect execution time
    // This is important to avoid stale ref values in the cleanup function
    const popupRootsMap = popupRootsRef.current;
    const currentMapInstance = mapInstanceRef.current;
    const currentMarkerCluster = markerClusterRef.current;

    return () => {
      // Set unmounting flag to true to prevent further operations
      unmountingRef.current = true;

      // Use a safer approach to clean up React roots
      // Schedule the unmounting to happen in the next tick to avoid React rendering conflicts
      setTimeout(() => {
        // Cleanup all React popup roots using the captured value
        popupRootsMap.forEach(root => {
          try {
            root.unmount();
          } catch (error) {
            // Silently catch errors during unmount to prevent crashes
            console.warn('Error unmounting popup root:', error);
          }
        });
        popupRootsMap.clear();
      }, 0);

      // Clean up marker cluster and map instance
      if (currentMarkerCluster) {
        try {
          currentMarkerCluster.clearLayers();
        } catch (error) {
          console.warn('Error clearing marker cluster:', error);
        }
      }

      // Remove event listeners and destroy the map instance
      if (currentMapInstance) {
        try {
          // Remove all event listeners to prevent memory leaks
          currentMapInstance.off('moveend');

          // Remove the map instance
          currentMapInstance.remove();
        } catch (error) {
          console.warn('Error removing map instance:', error);
        }
        mapInstanceRef.current = null;
        markerClusterRef.current = null;
      }
    };
  }, []);

  // Map background click clears selection
  useEffect(() => {
    const map = mapInstanceRef.current;
    if (!map) return;
    const handleMapClick = () => {
      if (onUnselect) onUnselect();
    };
    map.on('click', handleMapClick);
    return () => {
      try { map.off('click', handleMapClick); } catch {}
    };
  }, [onUnselect]);

  // Handle the search in visible area
  const handleSearchInArea = () => {
    if (mapInstanceRef.current && onSearchInArea) {
      const bounds = mapInstanceRef.current.getBounds();
      onSearchInArea({
        north: bounds.getNorth(),
        south: bounds.getSouth(),
        east: bounds.getEast(),
        west: bounds.getWest()
      });
      setShowSearchButton(false);
    }
  };

  return (
    <div ref={containerRef} className="relative h-full rounded-none md:rounded-lg overflow-hidden border border-gray-200 shadow-none md:shadow-sm mobile-map-edge-reset">
      <style jsx global>{`
        /* Marker styling */
        .custom-map-marker { background: transparent; border: none; }
        .marker-container { position: relative; width: 22px; height: 22px; transform: translateY(0); }

        /* Solid, pointerless single pin */
        .pin-dot {
          position: absolute;
          inset: 0;
          background: #0070F3; /* brand solid blue */
          border: 2px solid #ffffff; /* white ring */
          border-radius: 9999px;
          box-shadow: 0 6px 14px rgba(0, 0, 0, 0.25);
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .pin-center-dot { display: none; }

        /* Group marker styling */
        .custom-map-marker-group { background: transparent; border: none; }
        .cluster-badge {
          width: 30px;
          height: 30px;
          background: #0F3D8C; /* darker brand */
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid #ffffff;
          box-shadow: 0 4px 12px rgba(0,0,0,0.18);
        }
        .cluster-badge { gap: 4px; }
        .cluster-text {
          color: #ffffff;
          font-weight: 800;
          font-size: 13px;
          font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .marker-container-group { position: relative; width: 28px; height: 28px; }
        /* Medical center exact-location group (multiple doctors at same coordinate) */
        .center-group-circle {
          width: 28px;
          height: 28px;
          background: #ffffff; /* white fill to distinguish from clusters */
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.16);
          border: 2px solid #0070F3; /* brand ring */
        }
        /* Faint hospital cross behind count */
        .center-group-hospital { position: absolute; opacity: 0.28; pointer-events: none; }
        .center-group-text {
          color: #0070F3;
          font-weight: 800;
          font-size: 12px;
          letter-spacing: 0.2px;
          font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Legend styles (desktop only) */
        .map-legend {
          display: flex;
          align-items: center;
          gap: 10px;
          background: rgba(255,255,255,0.95);
          backdrop-filter: saturate(180%) blur(6px);
          border: 1px solid rgba(0,0,0,0.06);
          box-shadow: 0 4px 12px rgba(0,0,0,0.08);
          border-radius: 8px;
          padding: 8px 10px;
          color: #1c2533;
          font-size: 12px;
          line-height: 1.2;
        }
        .legend-item { display: inline-flex; align-items: center; gap: 6px; white-space: nowrap; }
        .legend-sep { height: 16px; width: 1px; background: #e5e7eb; }
        .legend-cluster {
          width: 16px; height: 16px; border-radius: 6px; background: #0F3D8C; border: 2px solid #ffffff;
          display: inline-flex; align-items: center; justify-content: center; box-shadow: 0 2px 6px rgba(0,0,0,0.12);
        }
        .legend-cluster::after {
          content: '';
          width: 6px; height: 6px; border-radius: 2px; background: rgba(255,255,255,0.9);
          box-shadow: inset 0 0 0 2px rgba(15,61,140,0.35);
        }
        .legend-center {
          width: 16px; height: 16px; border-radius: 9999px; background: #ffffff; border: 2px solid #0070F3;
          position: relative; display: inline-flex; align-items: center; justify-content: center;
          box-shadow: 0 2px 6px rgba(0,0,0,0.12);
        }
        .legend-center::before, .legend-center::after {
          content: '';
          position: absolute;
          background: #0070F3;
          opacity: 0.3;
        }
        .legend-center::before { width: 10px; height: 2px; }
        .legend-center::after { width: 2px; height: 10px; }

        /* Popup styling - simplified and more direct */
        .leaflet-popup-content-wrapper {
          padding: 0 !important;
          border-radius: 8px !important;
          box-shadow: 0 3px 14px rgba(0,0,0,0.25) !important;
          overflow: hidden !important;
        }

        .leaflet-popup-content {
          margin: 0 !important;
          width: auto !important;
        }

        .leaflet-popup-tip {
          background: white !important;
        }

        .leaflet-popup-close-button {
          display: none !important;
        }

        .map-popup-container {
          width: 100%;
          overflow: hidden;
        }

        /* Custom scrollbar for the grouped popup */
        .overflow-y-auto::-webkit-scrollbar {
          width: 6px;
        }

        .overflow-y-auto::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 10px;
        }

        .overflow-y-auto::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 10px;
        }

        .overflow-y-auto::-webkit-scrollbar-thumb:hover {
          background: #a1a1a1;
        }

        /* Hide attribution */
        .leaflet-control-attribution {
          display: none !important;
        }

        /* Fix z-index issues with map in mobile view */
        .leaflet-container {
          z-index: 1;
        }

        .leaflet-control-container {
          z-index: 10;
        }

        @media (max-width: 768px) {
          .leaflet-control-container .leaflet-top {
            top: 60px;
          }

          .leaflet-control-container .leaflet-top.leaflet-right {
            top: 10px;
          }

          /* Compact legend for mobile */
          .map-legend { font-size: 11px; padding: 6px 8px; gap: 8px; }
          .legend-sep { height: 14px; }
          .legend-cluster { width: 14px; height: 14px; border-radius: 5px; }
          .legend-center { width: 14px; height: 14px; }
          .legend-center::before { width: 9px; height: 2px; }
          .legend-center::after { width: 2px; height: 9px; }

          /* Remove outer card/border feel on mobile so it blends with page edges */
          :root .mobile-map-edge-reset {
            border-radius: 0 !important;
            border-left-width: 0 !important;
            border-right-width: 0 !important;
          }
        }
      `}</style>
      {isLoading ? (
        <div className="h-full min-h-[400px] md:min-h-[200px] w-full flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0070F3] mx-auto mb-2"></div>
            <p className="text-gray-500">Cargando mapa...</p>
          </div>
        </div>
      ) : mapPointsRef.current.length === 0 ? (
        <div className="h-full min-h-[400px] md:min-h-[200px] w-full flex items-center justify-center bg-gray-50">
          <div className="text-center p-4">
            <p className="text-gray-500 mb-2">No se encontraron ubicaciones para mostrar en el mapa.</p>
            <p className="text-sm text-gray-400">Intenta con otra búsqueda o filtros diferentes.</p>
          </div>
        </div>
      ) : (
        <>
          <div className="relative w-full h-[60vh] md:h-full" style={{ height: mobileMapHeight ? `${mobileMapHeight}px` : undefined }}>
            <div ref={mapRef} className="absolute inset-0" />
            {/* Mobile legend (anchored to the map container) */}
            <div className="flex md:hidden absolute bottom-3 left-3 z-[40]">
              <div className="map-legend">
                <div className="legend-item">
                  <span className="legend-cluster" aria-hidden="true"></span>
                  <span>Varios profesionales</span>
                </div>
                <span className="legend-sep" aria-hidden="true"></span>
                <div className="legend-item">
                  <span className="legend-center" aria-hidden="true"></span>
                  <span>Establecimiento</span>
                </div>
              </div>
            </div>
            {/* Desktop legend */}
            <div className="hidden md:flex absolute bottom-4 left-4 z-[40]">
              <div className="map-legend">
                <div className="legend-item">
                  <span className="legend-cluster" aria-hidden="true"></span>
                  <span>Varios profesionales</span>
                </div>
                <span className="legend-sep" aria-hidden="true"></span>
                <div className="legend-item">
                  <span className="legend-center" aria-hidden="true"></span>
                  <span>Establecimiento</span>
                </div>
              </div>
            </div>
          </div>
          {showSearchButton && onSearchInArea && (
            <div className="absolute top-4 left-0 right-0 flex justify-center z-[50]">
              <Button
                onClick={handleSearchInArea}
                className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white shadow-md"
              >
                <Search className="h-4 w-4 mr-2" />
                Buscar en esta zona
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}